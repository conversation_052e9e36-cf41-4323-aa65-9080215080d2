package com.ybmmarket20.xyyreport.spm

import android.os.Bundle
import androidx.fragment.app.FragmentActivity
import com.ybmmarket20.xyyreport.session.SessionManager

open class XyyReportActivity: FragmentActivity(), IPageParams {

    //当前页面spm信息
    private var mSpmCnt: SpmBean? = SpmBean(SpmConstant.SPM_A, null, SpmConstant.SPM_DEFAULT, SpmConstant.SPM_DEFAULT, null)
    var mScmCnt: ScmBean? = ScmBean()
    var mExtensionMap: MutableMap<String, Any?> = mutableMapOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    open fun assembleSpmCnt(spmA:String?,spmB:String?,spmC:String?,spmD:String?,spmE:String?):String{
        var spmCnt = ""
        spmCnt += "${spmA ?: "1_1"}."
        spmCnt += "${spmB ?: "0"}."
        spmCnt += "${spmC ?: "0"}."
        spmCnt += "${spmD ?: "0"}."
        spmCnt += spmE?:"0"
        return spmCnt
    }

    override fun getSpmCtn(): SpmBean? {
        val spmE = mSpmCnt?.spmE
        spmE?.apply {
            if (!this.startsWith(SessionManager.get().getSession())) {
                //session有变化
                val randomStr = if (this.length == 14) {
                    this.substring(8, 14)
                } else SessionManager.get().newPageSession()
                mSpmCnt?.spmE = "${SessionManager.get().getSession()}$randomStr"
            }
        }
        return mSpmCnt
    }

    override fun getSpmCtnNewInstance(): SpmBean? {
        return getSpmCtn()?.newInstance()
    }

    override fun setSpmCtn(spm: SpmBean?) {
        mSpmCnt = spm
    }

    override fun getSpmExtra(): Map<String, String?> {
        return mapOf()
    }

    fun setScmCnt(scm: ScmBean?) {
        mScmCnt = scm
    }

    fun getScmCnt(): ScmBean? {
        return mScmCnt
    }

    fun putExtension(key: String, value: Any?) {
        mExtensionMap[key] = value
    }

    fun getExtensionValue(key: String): Any? {
        return mExtensionMap[key]
    }

    fun getEnableAnalysis(): Boolean = onEnableAnalysis()

    fun containsSpmC(): Boolean {
        val spm = getSpmCtn()
        return spm?.spmC != SpmConstant.SPM_DEFAULT
    }

    open fun onEnableAnalysis(): Boolean = true

}