package com.ybm.app.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Looper;
import androidx.annotation.NonNull;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Toast;

import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.SmartExecutorManager;

import static android.view.View.NO_ID;


/**
 * Created by asus on 2016/3/10.
 */
public class UiUtils {
    private static Toast toast = null;

    /**
     * 获取屏幕宽度
     *
     * @return
     */
    public static int getScreenWidth() {
        try {
            return getContext().getResources().getDisplayMetrics().widthPixels;
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
        return 0;
    }

    /**
     * 获取屏幕高度 实际显示的高度 (去除顶部栏)
     *
     * @param context
     * @return
     */
    public static int getScreenShowHeight(Activity context) {
        Rect frame = new Rect();
        context.getWindow().getDecorView().getWindowVisibleDisplayFrame(frame);
        return getContext().getResources().getDisplayMetrics().heightPixels
                - frame.top;
    }

    public static boolean isTablet(Context context) {
        return (context.getResources().getConfiguration().screenLayout
                & Configuration.SCREENLAYOUT_SIZE_MASK)
                >= Configuration.SCREENLAYOUT_SIZE_LARGE;
    }

    /**
     * 获取屏幕高度 （可用高度 应用程序显示区域指定可能包含应用程序窗口的显示部分，不包括系统装饰。发现在一些全面屏手机上高度会少了十几个像素，在普通手机上是准确的。
     * 后来发现主要是虚拟按键的问题，getDisplayMetrics().heightPixels会将虚拟按键的高度忽略掉。点击去看到他的说明是The absolute height of the available display size in pixels.问题就在这个available上了。
     * <p>
     * 作者：虚假雨
     * 链接：https://www.jianshu.com/p/74a7a40437cb
     * 来源：简书
     * 著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。）
     *
     * @return
     */
    public static int getScreenHeight() {
        try {
            return getContext().getResources().getDisplayMetrics().heightPixels;
        } catch (Exception e) {
            BugUtil.sendBug(e);
        }
        return 0;
    }

    /**
     * 获得屏幕高度 （同理，还有getMetrics方法，在Android4.4之后会隐藏掉状态栏的高度，注释里还写了一大堆，像来自Activity的请求在多窗口模式下会小于物理高度（这又是另一个坑了））
     *
     * @return
     */
    public static int getMetricsScreenHeight() {
        WindowManager wm = (WindowManager) getContext()
                .getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getMetrics(outMetrics);
        return outMetrics.heightPixels;
    }

    /**
     * 获取真实的屏幕高度
     * <p>
     * float height = dm.heightPixels;
     */
    public static int getRealScreenHeight() {
        WindowManager wm = (WindowManager) getContext()
                .getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(outMetrics);
        return outMetrics.heightPixels;
    }

    /**
     * 获取popupwindow动态高度
     */
    public static int getPopupWindowHeight(View view) {
        if (isNavigationBarExist(BaseYBMApp.getApp().getCurrActivity())) {
            return getRealScreenHeight() - getView2ScreenHeight(view) - getNavigationHeight(getContext());
        } else {
            return getRealScreenHeight() - getView2ScreenHeight(view);
        }
    }

    private static final String NAVIGATION = "navigationBarBackground";

    // 该方法需要在View完全被绘制出来之后调用，否则判断不了
    //在比如 onWindowFocusChanged（）方法中可以得到正确的结果
    public static boolean isNavigationBarExist(@NonNull Activity activity) {
        ViewGroup vp = (ViewGroup) activity.getWindow().getDecorView();
        if (vp != null) {
            for (int i = 0; i < vp.getChildCount(); i++) {
                vp.getChildAt(i).getContext().getPackageName();
                if (vp.getChildAt(i).getId() != NO_ID && NAVIGATION.equals(activity.getResources().getResourceEntryName(vp.getChildAt(i).getId()))) {
                    return true;
                }
            }
        }
        return false;
    }

    public static int getNavigationHeight(Context activity) {
        if (activity == null) {
            return 0;
        }
        Resources resources = activity.getResources();
        int resourceId = resources.getIdentifier("navigation_bar_height",
                "dimen", "android");
        int height = 0;
        if (resourceId > 0) {
            //获取NavigationBar的高度
            height = resources.getDimensionPixelSize(resourceId);
        }
        return height;
    }

    /**
     * 获取状态栏高度
     *
     * @param activity
     */
    public static int getStautsHeight(Activity activity) {
        View view = activity.getWindow().getDecorView();
        // 获取状态栏高度
        Rect frame = new Rect();
        // 测量屏幕宽和高
        view.getWindowVisibleDisplayFrame(frame);
        int stautsHeight = frame.top;

        return stautsHeight;
    }

    /**
     * 获取某个控件的宽高 调用方法后用View.getMeasuredWidth(),View.getMeasuredHeight() 来获取宽高
     *
     * @param child
     */
    public static void measureView(View child) {
        ViewGroup.LayoutParams p = child.getLayoutParams();
        if (p == null) {
            p = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.FILL_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        int childWidthSpec = ViewGroup.getChildMeasureSpec(0, 0 + 0, p.width);
        int lpHeight = p.height;
        int childHeightSpec;
        if (lpHeight > 0) {
            childHeightSpec = View.MeasureSpec.makeMeasureSpec(lpHeight,
                    View.MeasureSpec.EXACTLY);
        } else {
            childHeightSpec = View.MeasureSpec.makeMeasureSpec(0,
                    View.MeasureSpec.UNSPECIFIED);
        }
        child.measure(childWidthSpec, childHeightSpec);
    }

    /**
     * 获取控件顶部到屏幕的高度
     *
     * @param view
     * @return
     */
    public static int getViewTop2ScreenHeight(View view) {
        if (view == null) {
            return 0;
        }
        final int[] location = new int[2];
        view.getLocationOnScreen(location);
        return location[1];
    }

    /**
     * 获取控件左侧到屏幕左侧的距离
     *
     * @param view
     * @return
     */
    public static int getViewLeft2ScreenWidth(View view) {
        if (view == null) {
            return 0;
        }
        final int[] location = new int[2];
        view.getLocationOnScreen(location);
        return location[0];
    }

    /**
     * 获取控件底部到屏幕的高度
     *
     * @param view
     * @return
     */
    public static int getView2ScreenHeight(View view) {
        int y = getViewTop2ScreenHeight(view);
        if (view.getMeasuredHeight() == 0) {
            measureView(view);
        }
        return y + view.getMeasuredHeight();
    }

    /**
     * 获取控件底部到屏幕的底部的距离
     *
     * @param view
     * @return
     */
    public static int getView2ScreenBottomHeight(View view) {
        int y = getView2ScreenHeight(view);
        return getScreenHeight() - y;
    }

    /**
     * 将xml转换成view对象
     *
     * @param resId
     * @return
     */
    public static View getXmlView(int resId) {
        return View.inflate(getContext(), resId, null);
    }

    /**
     * 1dp=1px;
     * 1dp=0.5px;
     * 1dp=0.75px;
     * 1dp=1.5px;
     *
     * @param dp
     * @return
     */
    public static int dp2px(int dp) {
        return (int) (dp * getDensity() + 0.5);
    }


    public static int px2dp(int px) {
        return (int) (px / getDensity() + 0.5);
    }

    public static float getDensity() {
        float density = getScreenWidth() * 1.0f / 360;
        if (density <= 0.3) {
            density = getContext().getResources().getDisplayMetrics().density;
        }
        return density;
    }

    // 将px值转换为sp值，保证文字大小不变
    public static int px2sp(float pxValue) {
        final float fontScale = getContext().getResources().getDisplayMetrics().scaledDensity;
        return (int) (pxValue / fontScale + 0.5f);
    }


    /*
    正在toast就不让再次toast
     */
    private static void toastBg(String text, boolean isLong) {
        if (toast == null) {
            toast = Toast.makeText(getContext(), text, isLong ? Toast.LENGTH_LONG : Toast.LENGTH_SHORT);
        } else {
            toast.setText(text);
        }
        toast.show();
    }

    /*
        正在toast就不让再次toast
     */
    public static void toast(final String text, final boolean isLong) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            toastBg(text, isLong);
        } else {
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    toastBg(text, isLong);
                }
            });
        }
    }


    public static void toast(final String text) {
        toast(text, false);
    }


    /**
     * 获取字符串数组
     *
     * @param arrId
     * @return
     */
    public static String[] getStringArr(int arrId) {
        return getContext().getResources().getStringArray(arrId);
    }


    /**
     * 获取颜色
     *
     * @param colorId
     * @return
     */
    public static int getColor(int colorId) {
        return getContext().getResources().getColor(colorId);
    }

    /**
     * 获取颜色
     *
     * @param colorId
     * @return
     */
    public static int getColorInt(String colorStr) {
        int colorInt = 0x00000000;
        try {
             colorInt = Color.parseColor(colorStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return colorInt;
    }

    public static int[] getSize(View view) {
        int width = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        int height = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        view.measure(width, height);
        return new int[]{view.getMeasuredWidth(), view.getMeasuredHeight()};
    }

    public static DisplayMetrics getMetrics(Context mContext) {
        WindowManager mWM = (WindowManager) mContext.getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        mWM.getDefaultDisplay().getMetrics(displayMetrics);
        return displayMetrics;
    }

    public static Context getContext() {
        return BaseYBMApp.getAppContext();
    }

}
