package com.ybm.app.common;

import static com.ybm.app.common.BaseYBMApp.getAppContext;

import android.os.Looper;
import android.os.SystemClock;
import android.text.TextUtils;

import com.tencent.bugly.network.BuglyListenerFactory;
import com.ybm.app.BuildConfig;
import com.ybm.app.bean.HttpResponse;
import com.ybm.app.bean.NetError;
import com.ybm.app.bean.OKHttpRequestParams;
import com.ybm.app.bean.TimeLog;
import com.ybm.app.common.apicache.ApiCacheManager;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.utils.NetUtil;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import okhttp3.Dispatcher;
import okhttp3.Dns;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.internal.Util;

/**
 * 有基本功能的网络请求，没有缓存与数据解析的功能
 */
public class OkHttpManager {
    private static final String TAG = "OKHttpManager";
    /*
    1，只走网络，并且不缓存数据,接口不用配置
	2，只走网络，但是缓存数据 （下拉刷新）
    3，有缓存走缓存，并走网络（通用方式）,没有网络会显示缓存
    4，有缓存走缓存，不走网络，没有缓存或者过期才走网络，,没有网络会显示缓存 (只有这个方式才有时间到期)
    */
    public static final int NO_CACHE = 1;
    public static final int STORE_ONLY = 2;
    public static final int CACHE_AND_NET = 3;
    public static final int CACHE_ELSE_NET = 4;
    private static OkHttpManager httpManager;
    private OkHttpClient client;
    private final static int MAXTIMEOUT = 30;//最大超时时间 单位秒
    private final static int MAXTHREAD = 30;//最大并发

    private ExecutorService executorService;

    private OkHttpManager() {
        init();
    }

    public static OkHttpManager getInstance() {
        if (httpManager == null) {
            synchronized (OkHttpManager.class) {
                if (httpManager == null) {
                    httpManager = new OkHttpManager();
                }
            }
        }
        return httpManager;
    }


    class MyIgnorePolicy implements RejectedExecutionHandler {
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            // 可做日志记录等
            BugUtil.sendBug(new RejectedExecutionException(r.toString() + "线程池已满 rejected"));
        }
    }

    private void init() {

        executorService = new ThreadPoolExecutor(8, 100, 60, TimeUnit.SECONDS,
                new SynchronousQueue<Runnable>(), Util.threadFactory("OkHttp Dispatcher", false), new MyIgnorePolicy());

        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder()
                .eventListenerFactory(BuglyListenerFactory.getInstance())
                .dispatcher(new Dispatcher(executorService))
                .connectTimeout(MAXTIMEOUT, TimeUnit.SECONDS)
                .readTimeout(MAXTIMEOUT, TimeUnit.SECONDS).writeTimeout(MAXTIMEOUT, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .addInterceptor(new RequestParamsInterceptor())
                .addInterceptor(new AppInterceptor())
                .addInterceptor(new UserAgentInterceptor())
                .addInterceptor(new ApiMonitorInterceptor())
                .addInterceptor(new WafInterceptor())
                .addNetworkInterceptor(new NetInterceptor())
                .dns(new YBMDns());
        client = clientBuilder.build();
        client.dispatcher().setMaxRequestsPerHost(MAXTHREAD);

    }

    public OkHttpClient getClient() {
        return client;
    }

    /**
     * 设置最大并发
     *
     * @param count
     */
    public void setMaxRequests(int count) {
        if (count <= 0) {
            return;
        }
        client.dispatcher().setMaxRequestsPerHost(count);
    }


    /**
     * 异步请求
     *
     * @param params
     * @param callback 回调
     * @return
     */
    public void fetch(int cacheMode, OKHttpRequestParams params, final BaseCallback callback) {
        if (cacheMode < NO_CACHE || cacheMode > CACHE_ELSE_NET) {
            cacheMode = NO_CACHE;
        }
        boolean noNetShowCache = false;
        if (cacheMode == CACHE_AND_NET || cacheMode == CACHE_ELSE_NET) {
            noNetShowCache = true;
        }
        boolean storeNetData = (cacheMode > NO_CACHE);
        if (NetUtil.getNetworkState(getAppContext()) == NetUtil.NETWORN_NONE) {//没有网络
            if (noNetShowCache && callback != null) {//没有网络,但是走缓存
                Request request = handlerRequestError(params, true, callback);
                if (request == null) {
                    return;
                }
                fetch4Cache(false, false, request, callback);
            } else {
                SmartExecutorManager.getInstance().executeUI(new Runnable() {
                    @Override
                    public void run() {
                        if (callback != null) {
                            callback.onFailure(NetError.newNoNetwork(), null);
                        }
                    }
                });
            }
            return;
        }
        Request request = handlerRequestError(params, storeNetData, callback);
        if (request == null) {
            return;
        }
        if (callback == null) {
            client.newCall(request).enqueue(new BaseCallback() {
                @Override
                public void onFailure(NetError error, Request request) {

                }

                @Override
                public void onSuccess(HttpResponse response) {

                }
            });
        } else {//有网络并且回调不空的情况
            switch (cacheMode) {
                case NO_CACHE:
                case STORE_ONLY:
                    fetch4Net(request, callback);
                    return;
                case CACHE_AND_NET:
                    fetch4Net(request, callback);
                    fetch4Cache(true, false, request, callback);
                    return;
                case CACHE_ELSE_NET:
                    fetch4Cache(true, true, request, callback);
                    return;
            }
        }

    }

    private Request handlerRequestError(OKHttpRequestParams params, boolean storeNetData, final BaseCallback callback) {
        Request request = null;
        try {
            request = params.createRequest(storeNetData);
        } catch (Exception e) {
            BugUtil.sendBug(e);
            String str = e.getMessage();
            NetError error = null;
            if (e instanceof IllegalArgumentException && TextUtils.isEmpty(str) && str.contains("unexpected url: ")) {
                error = new NetError(NetError.URL_ERROR, 0, "请求地址异常");
            } else {
                error = new NetError(NetError.GENERIC_ERROR, 0, str);
            }
            final NetError finalError = error;
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    if (callback != null) {
                        callback.onFailure(finalError, null);
                    }
                }
            });
        }
        return request;
    }


    private void fetch4Net(final Request request, final BaseCallback callback) {
        client.newCall(request).enqueue(callback);
    }


    //从缓存返回数据
    private void fetch4Cache(final boolean hasNet, final boolean goNet, final Request request, final BaseCallback callback) {
        if (request == null || callback == null || request.tag() == null || !(request.tag() instanceof TimeLog)) {
            return;
        }
        if (Looper.getMainLooper() == Looper.myLooper()) {//ui线程
            fetch4CacheBg(hasNet, goNet, request, callback);
        } else {
            SmartExecutorManager.getInstance().executeUI(new Runnable() {
                @Override
                public void run() {
                    fetch4CacheBg(hasNet, goNet, request, callback);
                }
            });
        }
    }

    private void fetch4CacheBg(final boolean hasNet, final boolean goNet, final Request request, final BaseCallback callback) {
//        LogUtils.d("时间："+(SystemClock.elapsedRealtime()-((TimeLog)request.tag()).start));
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                String key = ((TimeLog) request.tag()).getCacheKey();
                String content = ApiCacheManager.getInstance().getCache(key);
                if (TextUtils.isEmpty(content)) {//没有缓存数据
                    if (!hasNet) {//没有网络的时候还要提示
                        SmartExecutorManager.getInstance().executeUI(new Runnable() {
                            @Override
                            public void run() {
                                callback.onFailure(NetError.newNoNetwork(), null);
                            }
                        });
                    }
                    if (goNet) {
                        fetch4Net(request, callback);
                    }
                } else {//返回缓存数据
                    ((TimeLog) request.tag()).setCachMd5(content);
                    callback.succ(new HttpResponse(request, true, 200, content));
                }
            }
        });
    }


    /**
     * 同步请求
     *
     * @param params
     * @return
     */
    public Response fetchSyn(OKHttpRequestParams params) throws Exception {
        if (NetUtil.getNetworkState(getAppContext()) == NetUtil.NETWORN_NONE) {//没有网络
            return null;
        }
        return client.newCall(params.createRequest(false)).execute();
    }


    //网络层 拦截器 在读取数据之前执行
    public static class NetInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            if (request != null && request.tag() != null && request.tag() instanceof TimeLog) {
                ((TimeLog) request.tag()).step3 = SystemClock.elapsedRealtime();
            }
            return chain.proceed(chain.request());
        }
    }


    //应用层拦截器 在 发送请求之前
    public static class AppInterceptor implements Interceptor {
        public static final String LIB_NEW = "new";
        public static final String LIB_OLD = "old";
        private String requestLib = LIB_OLD;

        public AppInterceptor() {
            super();
        }

        public AppInterceptor(String lib) {
            super();
            requestLib = lib;
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            String strUrl = request.url().toString();
            if (request != null && request.tag() != null && request.tag() instanceof TimeLog) {
                ((TimeLog) request.tag()).step2 = SystemClock.elapsedRealtime();
                ((TimeLog) request.tag()).url = strUrl;
            }
//            reportRequest(strUrl);
            Response response = chain.proceed(request);
            if (request != null && request.tag() != null && request.tag() instanceof TimeLog) {
                ((TimeLog) request.tag()).step4 = SystemClock.elapsedRealtime();
            }
            return response;
        }
/*
        public void reportRequest(String url) {
            if (!TextUtils.isEmpty(url)) {
                int lastIndex = url.lastIndexOf("?");
                if (lastIndex > 0) {
                    url = url.substring(0, lastIndex);
                }
                JSONObject jsObj = new JSONObject();
                try {
                    jsObj.put("url", url);
                    jsObj.put("type", requestLib);
                } catch (JSONException ex) {
                    // donothing;
                }
                XyyIoSDK.getInstance().track("ybm_request", jsObj);
            }
        }
 */
    }


    //域名解析，可以扩展一个域名多个ip 地址，或者自己解析域名，解决网络不好，域名解析不出来的问题
    public static class YBMDns implements Dns {

        @Override
        public List<InetAddress> lookup(String hostname) throws UnknownHostException {
            if (hostname == null) throw new UnknownHostException("host == null");
//            InetAddress address = null;
//            try {
//                address = InetAddress.getByName(hostname);
////                LogUtils.d(address.toString());
//            } catch (Exception e) {
//                throw new UnknownHostException("missing INTERNET permission");
//            }
            return Arrays.asList(InetAddress.getAllByName(hostname));
        }
    }

}
