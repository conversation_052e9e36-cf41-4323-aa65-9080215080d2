package com.ybm.app.adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.apkfuns.logutils.LogUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.chad.library.adapter.base.loadmore.LoadMoreView;
import com.chad.library.adapter.base.loadmore.SimpleLoadMoreView;
import com.ybm.app.R;
import com.ybm.app.utils.BugUtil;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
public abstract class YBMBaseAdapter<T> extends BaseQuickAdapter {
    private View mContentView;
    private View noData;
    private boolean isShowNoData = true;
    private LoadMoreView loadingView = null;

    private int mLResId = 0;

    public YBMBaseAdapter(List<T> data) {
        this(0, data);
    }

    public YBMBaseAdapter(int layoutResId, List<T> data) {
        super(layoutResId, data);
        mLResId = layoutResId;
    }
    public YBMBaseAdapter(View contentView, List<T> data) {
        this(0, data);
        this.mContentView = contentView;
    }

    @Override
    public BaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        //自定义加载更多view
        if (viewType == 546) {
            if (loadingView == null) {
                loadingView = new SimpleLoadMoreView();
//                  loadingView = View.inflate(parent.getContext(), R.layout.view_loadmore, null);
//                ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, dp2px(parent.getContext(), 45));
//                loadingView.setLayoutParams(lp);
//                setLoadingView(loadingView);
                setLoadMoreView(loadingView);
            }
        }
            return super.onCreateViewHolder(parent, viewType);
    }

    public int dp2px(Context context, float dpValue) {
        if (context == null) return 0;
        final float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

    public void setlayoutResId(int layoutResId) {
        mLayoutResId = layoutResId;
        mLResId = layoutResId;
    }

    public int getLayoutResId() {
        return mLayoutResId;
    }

    public void setShowNoData(boolean isShowNoData) {
        this.isShowNoData = isShowNoData;
    }

    @Override
    protected BaseViewHolder createBaseViewHolder(ViewGroup parent, int layoutResId) {
        return this.mContentView == null ? new YBMBaseHolder(this.getItemView(layoutResId, parent)) : new YBMBaseHolder(this.mContentView);
    }


    public void notifyDataChangedAfterLoadMore(boolean enableLoadNext) {

        notifyDataSetChanged();

        if (enableLoadNext) {
            loadMoreComplete();
        } else {
            loadMoreEnd(true);
        }
    }

    /**
     * @param isFirstRefresh 是否是分页请求的第一次请求，如果是第一次请求，且没有下一页，不展示【没有更多数据】
     * @param enableLoadNext 是否开启加载下一页
     */
    public void notifyDataChangedAfterLoadMore(boolean isFirstRefresh, boolean enableLoadNext) {

        notifyDataSetChanged();

        if (enableLoadNext) {
            loadMoreComplete();
        } else {
            loadMoreEnd(isFirstRefresh);
        }
    }

    @Override
    protected void convert(BaseViewHolder baseViewHolder, Object bena) {
        bindItemView((YBMBaseHolder) baseViewHolder, (T) bena);
    }

    @Override
    protected void convertPayloads(@NonNull BaseViewHolder helper, Object item, @NonNull List payloads) {
        bindItemView((YBMBaseHolder) helper, (T) item,payloads);
    }

    protected abstract void bindItemView(YBMBaseHolder baseViewHolder, T t);
    protected void bindItemView(YBMBaseHolder baseViewHolder, T t,@NonNull List payloads){

    }

    public void setEmptyView(Context context, int layoutId, int resId, String text) {
        if (layoutId > 0) {
            try {
                View e = View.inflate(context, layoutId, null);
                if (e == null) {
                    return;
                }
                ViewGroup.LayoutParams layoutParams = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
                e.setLayoutParams(layoutParams);
                ImageView imageView = (ImageView) e.findViewById(R.id.iv);
                if (imageView != null) {
                    imageView.setImageResource(resId);
                }

                if (text != null) {
                    TextView textView = (TextView) e.findViewById(R.id.tv);
                    if (textView != null) {
                        textView.setText(text);
                    }
                }
                setEmptyView(e);
            } catch (Throwable var7) {
                LogUtils.d(var7);
            }

        }
    }

    @Override
    public void setNewData(List data) {
        if (data == null) {
            data = new ArrayList();
        }
        try {
            super.setNewData(data);
        } catch (Throwable e) {
            BugUtil.sendBug(e);
        }

    }

    public void openLoadMore(int size, boolean isloadmore) {
        setEnableLoadMore(isloadmore);
    }

    /**
     * 解决第一页刷新没有更多数据时，触发loadmore的逻辑
     */
    @Override
    public void loadMoreEnd() {
        loadMoreEnd(true);
    }

}
