<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/base_bg"
    android:divider="@drawable/divider_line_w_1px"
    android:orientation="vertical"
    android:showDividers="middle">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:id="@+id/ll_set_notification"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:background="@color/white"
        android:minHeight="44dp"
        android:paddingLeft="10dp"
        android:paddingRight="10dp">

        <TextView
            style="@style/about_layout_tv"
            android:layout_below="@id/tv"
            android:paddingLeft="0dp"
            android:text="通知栏快捷入口"
            android:textColor="@color/text_292933"
            android:textSize="15sp" />

        <CheckBox
            android:id="@+id/cb_setting"
            style="@style/addressCheckboxTheme"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:clickable="false" />
    </RelativeLayout>

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/order_gray_border"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="开启后，你可以在手机通知栏中快捷使用扫码进货，快速查看待收货的物流"
        android:textColor="@color/text_9494A6"
        android:textSize="14sp" />
</LinearLayout>