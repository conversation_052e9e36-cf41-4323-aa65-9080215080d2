<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="452dp"
    android:background="@drawable/shape_white_4_4_0_0"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="选择赠品"
        android:gravity="center"
        android:textSize="17dp"
        android:textColor="@color/color_292933"
        android:textStyle="bold"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_height="46dp"/>

    <ImageView
        android:id="@+id/iv_close"
        android:src="@drawable/icon_seckill_grey_close"
        android:layout_width="34dp"
        app:layout_constraintTop_toTopOf="@id/tv_title"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        android:padding="10dp"
        android:layout_marginEnd="6dp"
        android:layout_height="34dp"/>

    <FrameLayout
        android:id="@+id/framelayout_gift_select"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintBottom_toTopOf="@id/tv_confirm"
        android:layout_width="0dp"
        android:layout_height="0dp"/>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_confirm"
        app:layout_constraintTop_toBottomOf="@id/framelayout_gift_select"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="0dp"
        android:layout_marginHorizontal="14dp"
        android:layout_marginVertical="8dp"
        app:rv_backgroundColor="@color/color_00B955"
        app:rv_cornerRadius="5dp"
        android:gravity="center"
        android:textSize="14dp"
        android:text="确定"
        android:textColor="@color/white"
        android:layout_height="34dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>