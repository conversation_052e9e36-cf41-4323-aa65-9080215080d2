<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        
        <TextView
            android:id="@+id/tv_tab"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:textSize="15dp"
            android:gravity="center"
            android:textColor="@color/black"
            android:layout_width="wrap_content"
            app:layout_constraintEnd_toStartOf="@id/guide_line"
            tools:text="萨拉睡觉了"
            android:layout_height="wrap_content"/>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintGuide_end="12dp"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="0dp"/>

        <ImageView
            android:id="@+id/iv_tip"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:src="@color/black"
            android:visibility="gone"
            android:layout_width="16dp"
            android:layout_height="8dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
