<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFAFAFA"
        android:elevation="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_scrollFlags="scroll|exitUntilCollapsed|snap">

            <RelativeLayout
                android:id="@+id/rl_shop_header"
                style="@style/header_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:paddingTop="0dp">

                <LinearLayout
                    android:id="@+id/ll_about"
                    android:layout_width="250dp"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/shop_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableRight="@drawable/icon_shop_right"
                        android:enabled="true"
                        android:maxLines="1"
                        android:paddingLeft="10dp"
                        android:singleLine="true"
                        android:textColor="#FF292933"
                        android:textSize="18sp"
                        tools:text="广州医药集团有限公司" />

                </LinearLayout>

                <com.ybmmarket20.common.widget.RoundTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginRight="11dp"
                    android:layout_toLeftOf="@+id/ll_shop_01"
                    android:background="@drawable/icon_shop_add"
                    android:drawableLeft="@drawable/icon_shop_add"
                    android:paddingLeft="9dp"
                    android:paddingTop="5dp"
                    android:paddingRight="9dp"
                    android:paddingBottom="5dp"
                    android:text="关注"
                    android:textColor="@color/text_292933"
                    android:textSize="14sp"
                    android:visibility="gone"
                    app:rv_cornerRadius="16dp"
                    app:rv_strokeColor="#dedede"
                    app:rv_strokeWidth="1dp" />

                <com.ybmmarket20.common.widget.RoundLinearLayout
                    android:id="@+id/ll_shop_01"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="13dp"
                    android:orientation="horizontal"
                    android:paddingLeft="7dp"
                    android:paddingRight="9dp"
                    app:rv_cornerRadius="16dp"
                    app:rv_strokeColor="#dedede"
                    app:rv_strokeWidth="1dp">

                    <ImageView
                        android:id="@+id/iv_pop"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/icon_detail_right_b" />

                    <View
                        android:layout_width="1px"
                        android:layout_height="20dp"
                        android:layout_gravity="center_vertical"
                        android:background="#E5E5E5" />

                    <ImageView
                        android:id="@+id/iv_back"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginLeft="5dp"
                        android:src="@drawable/icon_shop_close" />

                </com.ybmmarket20.common.widget.RoundLinearLayout>

            </RelativeLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:orientation="vertical"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp">

                    <TextView
                        android:id="@+id/category_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_shop_count"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp"
                        android:textColor="@color/text_676773"
                        android:textSize="12sp"
                        tools:text="上架 938种" />

                    <TextView
                        android:id="@+id/sales_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="6dp"
                        android:background="@drawable/bg_shop_count"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp"
                        android:textColor="@color/text_676773"
                        android:textSize="12sp"
                        tools:text="销量 938万件" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_sales_origin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:background="@drawable/icon_sales_origin"
                        android:paddingLeft="5dp"
                        android:paddingTop="1dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="1dp"
                        android:text="优惠"
                        android:textColor="#F76A24"
                        android:textSize="10sp" />

                    <TextView
                        android:id="@+id/sales_origin"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_marginTop="8dp"
                        android:layout_weight="1"
                        android:minLines="1"
                        android:paddingLeft="2dp"
                        android:paddingRight="2dp"
                        android:singleLine="true"
                        android:textColor="#F76A24"
                        android:textSize="11sp"
                        tools:text="200元起送，满500元包邮" />

                    <ImageView
                        android:id="@+id/iv_share_shop"
                        android:layout_width="@dimen/dimen_dp_22"
                        android:layout_height="@dimen/dimen_dp_22"
                        android:layout_marginRight="@dimen/dimen_dp_15"
                        android:gravity="center"
                        android:src="@drawable/icon_shop_share" />

                    <ImageView
                        android:id="@+id/iv_link_shop_user"
                        android:layout_width="@dimen/dimen_dp_22"
                        android:layout_height="@dimen/dimen_dp_22"
                        android:gravity="center"
                        android:src="@drawable/icon_link_shop_user" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_discount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="11dp"
                    android:visibility="gone" />

            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#FFFAFAFA"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">


        <LinearLayout
            android:id="@+id/ll_title"
            style="@style/header_layout"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:visibility="gone"
            tools:visibility="visible">

            <RelativeLayout
                android:id="@+id/rl_cart"
                style="@style/header_layout_right_img"
                android:layout_width="56dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp">

                <ImageView
                    android:id="@+id/iv_cart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:src="@drawable/icon_seckill_grey_cart" />

                <TextView
                    android:id="@+id/tv_num"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="2dp"
                    android:background="@drawable/bg_message"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="gone" />
            </RelativeLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="#E5E5E5" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="20dp"
                android:background="@drawable/layer_shop_search_bg"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/bt_search"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:padding="5dp"
                    android:src="@drawable/nav_scarch" />

                <TextView
                    android:id="@+id/et_search"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:singleLine="true"
                    android:text="搜索"
                    android:textColor="@color/color_9494A6"
                    android:textSize="13sp" />

            </LinearLayout>

            <com.flyco.tablayout.SlidingTabLayout
                android:id="@+id/tabLayout"
                android:layout_width="wrap_content"
                android:layout_height="37dp"
                app:tl_indicator_color="@color/color_00b377"
                app:tl_indicator_height="@dimen/dimen_dp_3"
                app:tl_indicator_width="@dimen/dimen_dp_20"
                app:tl_textBold="SELECT"
                app:tl_textSelectColor="@color/color_292933"
                app:tl_textSelectSize="@dimen/dimen_dp_15"
                app:tl_textUnselectColor="@color/color_676773"
                app:tl_textsize="@dimen/dimen_dp_14" />

        </LinearLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>