<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_detail"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/activity_bg"
        android:orientation="vertical">
        <!--android:id="@+id/ll_title" id不要随便换BaseActivity会用到-->
        <LinearLayout
            android:id="@+id/ll_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/header_search_height"
            android:background="@drawable/base_header_default_bg"
            android:orientation="horizontal"
            android:paddingTop="@dimen/header_height_padding_top">

            <RelativeLayout
                android:id="@+id/title_left"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingLeft="10dp"
                android:visibility="visible">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerVertical="true"
                    android:minWidth="54dp"
                    android:src="@drawable/ic_back" />

            </RelativeLayout>

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/ps_tab"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="4"
                app:tabGravity="center"
                app:tabIndicatorColor="@color/tv_tab_color"
                app:tabIndicatorHeight="2dp"
                app:tabMaxWidth="100dp"
                app:tabMinWidth="50dp"
                app:tabMode="fixed"
                app:tabSelectedTextColor="@color/tv_tab_color"
                app:tabTextColor="@color/white" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="8"
                android:gravity="center"
                android:text="商品不存在"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/tv_menu"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="right|center_vertical"
                android:orientation="horizontal"
                android:paddingRight="10dp">

                <ImageView
                    android:id="@+id/tv_menu1"
                    style="@style/detail_service_spot_layout" />

                <ImageView
                    android:id="@+id/tv_menu2"
                    style="@style/detail_service_spot_layout"
                    android:layout_marginLeft="1.6dp" />

                <ImageView
                    android:id="@+id/tv_menu3"
                    style="@style/detail_service_spot_layout"
                    android:layout_marginLeft="1.6dp" />

            </LinearLayout>
        </LinearLayout>

        <include layout="@layout/empty_view_comment" />

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/vp_client"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white" />

    </LinearLayout>

</LinearLayout>
