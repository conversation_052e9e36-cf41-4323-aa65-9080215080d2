<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="182dp"
            android:layout_weight="1"
            android:background="@color/colors_f4f4f4"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal">

                <LinearLayout
                    android:id="@+id/ll_share_link_url"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_share_link_url"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:src="@drawable/icon_share_link_url" />

                    <TextView
                        android:id="@+id/tv_share_download"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="2dp"
                        android:text="复制链接"
                        android:textColor="@color/text_9494A6"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_share_wx"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_share_wx"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:src="@drawable/icon_share_wx_invitation" />

                    <TextView
                        android:id="@+id/tv_share_wx"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="2dp"
                        android:text="微信好友"
                        android:textColor="@color/text_9494A6"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/ll_share_wxpyq"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/iv_share_wxpyq"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:src="@drawable/icon_share_wx_invitation2" />

                    <TextView
                        android:id="@+id/tv_share_wxpyq"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="2dp"
                        android:text="微信朋友圈"
                        android:textColor="@color/text_9494A6"
                        android:textSize="12sp" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_cancel"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center"
            android:text="取消"
            android:textColor="@color/color_292933"
            android:textSize="17sp"
            app:rv_backgroundColor="@color/white"
            app:rv_strokeColor="#cccccc"
            app:rv_strokeWidth="1px" />

    </LinearLayout>

</FrameLayout>