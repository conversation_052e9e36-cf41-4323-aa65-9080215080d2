<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="400dp"
        android:layout_gravity="center">

        <ImageView
            android:id="@+id/iv_1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:src="@drawable/icon_mine_gift_pop_01" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="110dp"
            android:background="@drawable/bg_mine_gift_pop_03"
            android:gravity="center"
            android:minHeight="15dp"
            android:minWidth="95dp"
            android:paddingBottom="2dp"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:paddingTop="2dp"
            android:text="2019.01.07-2019.03.05"
            android:textColor="#CF8F27"
            android:textSize="8dp" />

    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@drawable/icon_mine_gift_pop_02" />

</LinearLayout>