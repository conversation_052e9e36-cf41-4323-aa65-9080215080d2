<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/tv_mine2_amount_image"
        android:layout_width="@dimen/dimen_dp_22"
        android:layout_height="@dimen/dimen_dp_22"
        android:src="@drawable/icon_my_rich"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_mine2_des"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/tv_mine2_des"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_292933"
        tools:text="购物金"
        android:drawablePadding="@dimen/dimen_dp_2"
        android:layout_marginTop="@dimen/dimen_dp_3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_mine2_amount_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <View
        android:id="@+id/v_mine2_amount_divider"
        android:layout_width="@dimen/dimen_dp_1"
        android:layout_height="@dimen/dimen_dp_30"
        android:background="@color/colors_DDDDDD"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>