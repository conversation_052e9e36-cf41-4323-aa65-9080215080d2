<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingLeft="15dp"
    android:paddingRight="15dp">

    <TextView
        android:id="@+id/tv_take_photo"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@drawable/bg_comment_round_top_white"
        android:gravity="center"
        android:text="拍照"
        android:textColor="#1f6efe"
        android:textSize="15sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider"
        android:background="@color/divider_line_color_eeeeee" />

    <TextView
        android:id="@+id/tv_pic_photo"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@drawable/bg_comment_round_bottom_white"
        android:gravity="center"
        android:text="照片图库"
        android:textColor="#1f6efe"
        android:textSize="15sp" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="15dp"
        android:background="@drawable/bg_comment_round_white"
        android:gravity="center"
        android:text="取消"
        android:textColor="#1f6efe"
        android:textSize="15sp" />

</LinearLayout>