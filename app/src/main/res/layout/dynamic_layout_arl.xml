<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:gravity="center"
        android:text="动态布局标题"
        android:textSize="16sp" />
    <!--增加自己的固定布局-->
    <RelativeLayout
        android:id="@+id/rl_layout"
        android:clipChildren="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.ybmmarket20.view.ClipViewPager
            android:id="@+id/vp_arl"
            android:clipChildren="false"
            android:layout_centerHorizontal="true"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <LinearLayout
            android:id="@+id/ll_arl"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_alignParentBottom="true"
            android:gravity="center_vertical|right"
            android:paddingRight="10dp"
            android:orientation="horizontal" />
    </RelativeLayout>
</merge>