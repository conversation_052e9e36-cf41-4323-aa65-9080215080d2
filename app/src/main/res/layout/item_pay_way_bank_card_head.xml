<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_2"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius_TL="@dimen/dimen_dp_2"
        app:rv_cornerRadius_TR="@dimen/dimen_dp_2" />
</androidx.constraintlayout.widget.ConstraintLayout>