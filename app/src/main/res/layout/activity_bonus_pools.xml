<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/ps_tab"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@drawable/bonus_pools_bg"
        android:orientation="horizontal"
        app:tl_indicator_color="@color/tv_tab_color"
        app:tl_indicator_height="2dp"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_space_equal="true"
        app:tl_textAllCaps="true"
        app:tl_textSelectColor="@color/text_292933"
        app:tl_textSelectSize="16sp"
        app:tl_textUnselectColor="@color/text_9494A6"
        app:tl_textsize="14sp" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/vp_client"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white" />

</LinearLayout>