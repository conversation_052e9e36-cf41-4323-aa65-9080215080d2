<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <com.ybmmarket20.view.MyScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <View
                android:layout_width="match_parent"
                android:layout_height="10dp"
                android:background="#EFEFEF" />

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:rv_backgroundColor="@color/white">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:background="#FBFBFB"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="药帮忙平台购买信息"
                    android:textColor="@color/text_292933"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_order_id"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="订单编号："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_order_time"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="下单时间："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_order_name"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="商品名称："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_order_spec"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="规格："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_order_price"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="单价："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_order_num"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="采购数量："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <TextView
                    android:id="@+id/tv_order_amount"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="小计金额："
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="#FBFBFB"
                android:gravity="center_vertical"
                android:paddingLeft="10dp"
                android:text="其他平台此商品购买信息"
                android:textColor="@color/text_292933"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_supplier_name"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="41dp"
                android:paddingLeft="10dp"
                android:text="供货商姓名:"
                android:textColor="@color/text_292933"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/tv_supplier_phone"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="41dp"
                android:paddingLeft="10dp"
                android:text="供货商联系方式:"
                android:textColor="@color/text_292933"
                android:textSize="15sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:minHeight="100dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:text="您在其他平台购买此商品的随货同行单及发票照片"
                    android:textColor="@color/text_292933"
                    android:textSize="15sp" />

                <RelativeLayout
                    android:id="@+id/fragment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"/>

            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:background="#FBFBFB"
                android:gravity="center_vertical"
                android:paddingLeft="10dp"
                android:text="请核实您的联系方式"
                android:textColor="@color/text_292933"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="41dp"
                android:paddingLeft="10dp"
                android:text="联系人:"
                android:textColor="@color/text_292933"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:minHeight="41dp"
                android:paddingLeft="10dp"
                android:text="联系方式:"
                android:textColor="@color/text_292933"
                android:textSize="15sp" />

        </LinearLayout>
    </com.ybmmarket20.view.MyScrollView>
</LinearLayout>