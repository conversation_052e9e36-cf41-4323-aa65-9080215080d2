<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_balance_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingBottom="14dp"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    android:paddingTop="14dp">

    <TextView
        android:id="@+id/tv_balance_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#292933"
        android:textSize="14sp"
        tools:text="购物抵扣" />

    <TextView
        android:id="@+id/tv_balance_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_balance_type"
        android:layout_marginTop="2dp"
        android:textColor="#9494A6"
        android:textSize="14sp"
        tools:text="2017-03-29 16:37:00" />

    <TextView
        android:id="@+id/tv_balance_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginRight="20dp"
        android:textColor="#292933"
        android:textSize="14sp"
        tools:text="+1000" />

    <ImageView
        android:id="@+id/iv_right"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:src="@drawable/icon_right_gray" />
</RelativeLayout>