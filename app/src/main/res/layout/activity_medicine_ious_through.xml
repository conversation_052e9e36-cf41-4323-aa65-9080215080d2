<?xml version="1.0" encoding="utf-8"?>
<com.ybmmarket20.common.widget.RoundLinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/apply_status_through"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_hint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:lineSpacingExtra="2dp"
        android:minHeight="55dp"
        android:padding="10dp"
        android:textColor="@color/colors_99664D"
        android:textSize="12sp"
        app:rv_backgroundColor="@color/colors_fff7ef"
        tools:text="@string/medicine_ious_hint" />

    <com.ybmmarket20.common.widget.RoundRelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.ybmmarket20.common.widget.RoundedImageView
            android:id="@+id/iv_bg"
            android:layout_width="match_parent"
            android:layout_height="201dp"
            android:scaleType="centerCrop"
            android:src="@drawable/icon_bg_medicine_ious" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_lines_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="25dp"
            android:text="@string/medicine_ious_lines_hint"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_account_money"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_lines_info"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="5dp"
            android:textColor="@color/white"
            android:textSize="36sp"
            tools:text="*********" />

        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:id="@+id/ll_bg_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_account_money"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="50dp"
            android:layout_marginRight="10dp"
            android:elevation="2dp"
            android:minHeight="60dp"
            android:orientation="horizontal"
            app:rv_backgroundColor="@color/white"
            app:rv_cornerRadius="4dp"
            tools:targetApi="lollipop">

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll_a_combination"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="10dp">

                <TextView
                    android:id="@+id/tv_a_combination_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="@string/medicine_ious_a_combination_hint"
                    android:textColor="@color/color_9494A6"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_a_combination_money"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textColor="@color/color_292933"
                    android:textSize="16sp"
                    tools:text="1000000" />

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll_amount_to_be_also"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="10dp">

                <TextView
                    android:id="@+id/tv_amount_to_be_also_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:text="@string/medicine_ious_amount_to_be_also_hint"
                    android:textColor="@color/color_9494A6"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_amount_to_be_also_money"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:textColor="@color/color_292933"
                    android:textSize="16sp"
                    tools:text="1000000" />

            </com.ybmmarket20.common.widget.RoundLinearLayout>

        </com.ybmmarket20.common.widget.RoundLinearLayout>

        <com.ybmmarket20.common.widget.RoundLinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ll_bg_info"
            android:layout_marginTop="10dp"
            android:background="@color/white"
            android:orientation="vertical">

            <com.ybmmarket20.common.widget.RoundRelativeLayout
                android:id="@+id/rl_transaction_details"
                style="@style/medicineious_layout">

                <TextView
                    style="@style/medicineious_layout_tv"
                    android:text="@string/medicine_ious_transaction_details_hint" />

                <TextView
                    android:id="@+id/tv_transaction_details"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:textColor="@color/color_9494A6"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:text="@string/medicine_ious_empty_hint" />

                <com.ybmmarket20.common.widget.RoundedImageView
                    android:id="@+id/iv_transaction_details"
                    style="@style/medicineious_layout_iv"
                    android:src="@drawable/right_new" />
            </com.ybmmarket20.common.widget.RoundRelativeLayout>

            <com.ybmmarket20.common.widget.RoundRelativeLayout
                android:id="@+id/rl_interest_subsidy"
                style="@style/medicineious_layout">

                <TextView
                    style="@style/medicineious_layout_tv"
                    android:text="@string/medicine_ious_interest_subsidy_hint" />

                <TextView
                    android:id="@+id/tv_interest_subsidy"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="10dp"
                    android:textColor="@color/color_9494A6"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:text="@string/medicine_ious_empty_hint" />

                <com.ybmmarket20.common.widget.RoundedImageView
                    android:id="@+id/iv_interest_subsidy"
                    style="@style/medicineious_layout_iv"
                    android:src="@drawable/right_new" />
            </com.ybmmarket20.common.widget.RoundRelativeLayout>

            <com.ybmmarket20.common.widget.RoundRelativeLayout
                android:id="@+id/rl_common_problems"
                style="@style/medicineious_layout">

                <TextView
                    style="@style/medicineious_layout_tv"
                    android:text="@string/medicine_ious_common_problems_hint" />

                <com.ybmmarket20.common.widget.RoundedImageView
                    style="@style/medicineious_layout_iv"
                    android:src="@drawable/right_new" />
            </com.ybmmarket20.common.widget.RoundRelativeLayout>

        </com.ybmmarket20.common.widget.RoundLinearLayout>

    </com.ybmmarket20.common.widget.RoundRelativeLayout>

</com.ybmmarket20.common.widget.RoundLinearLayout>