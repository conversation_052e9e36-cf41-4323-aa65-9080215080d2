<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dimen_dp_55">

    <TextView
        android:id="@+id/tvNum"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:text="0"
        android:background="@drawable/selector_input_password_num"
        android:gravity="center"
        android:textSize="@dimen/dimen_dp_27"
        android:textColor="#292933"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>