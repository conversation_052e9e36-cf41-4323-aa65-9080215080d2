<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F7F7F7">

    <include
        android:id="@+id/header"
        layout="@layout/common_header_items" />

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dimen_dp_10"
        android:paddingBottom="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/header"
        app:rv_backgroundColor="@color/white"
        app:rv_cornerRadius="@dimen/dimen_dp_6">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="@dimen/dimen_dp_22"
            android:layout_height="@dimen/dimen_dp_22"
            android:layout_marginTop="@dimen/dimen_dp_13"
            android:layout_marginStart="@dimen/dimen_dp_15"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
        
        <TextView
            android:id="@+id/tvBankName"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_12"
            android:textColor="@color/color_292933"
            app:layout_constraintBottom_toBottomOf="@+id/ivLogo"
            app:layout_constraintTop_toTopOf="@+id/ivLogo"
            app:layout_constraintStart_toEndOf="@+id/ivLogo"
            android:textStyle="bold"
            tools:text="建设银行" />


        <TextView
            android:id="@+id/tvUserNameTitle"
            style="@style/setPayPwText"
            android:layout_marginTop="@dimen/dimen_dp_17"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:text="持卡人姓名"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvBankName" />

        <TextView
            android:id="@+id/tvUserNameTitleTips"
            style="@style/setPayPwTextTips"
            android:layout_marginStart="@dimen/dimen_dp_5"
            app:layout_constraintBottom_toBottomOf="@+id/tvUserNameTitle"
            app:layout_constraintStart_toEndOf="@+id/tvUserNameTitle"
            app:layout_constraintTop_toTopOf="@+id/tvUserNameTitle"
            tools:text="请输入正确的姓名" />

        <EditText
            android:id="@+id/etUserName"
            style="@style/setPayPwEdit"
            android:hint="请输入持卡人姓名"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:maxLength="30"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvUserNameTitle" />

        <ImageView
            android:id="@+id/ivUserNameClear"
            android:layout_width="@dimen/dimen_dp_44"
            android:layout_height="@dimen/dimen_dp_44"
            android:src="@drawable/clear_sousou"
            android:visibility="gone"
            android:padding="@dimen/dimen_dp_14"
            app:layout_constraintBottom_toBottomOf="@+id/etUserName"
            app:layout_constraintEnd_toEndOf="@+id/etUserName"
            app:layout_constraintTop_toTopOf="@+id/etUserName"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvUserIdCardTitle"
            style="@style/setPayPwText"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:text="持卡人身份证号"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etUserName" />

        <TextView
            android:id="@+id/tvUserIdCardTitleTips"
            style="@style/setPayPwTextTips"
            android:layout_marginStart="@dimen/dimen_dp_5"
            app:layout_constraintBottom_toBottomOf="@+id/tvUserIdCardTitle"
            app:layout_constraintStart_toEndOf="@+id/tvUserIdCardTitle"
            app:layout_constraintTop_toTopOf="@+id/tvUserIdCardTitle"
            tools:text="请输入正确的身份证号" />

        <EditText
            android:id="@+id/etUserIdCard"
            style="@style/setPayPwEdit"
            android:hint="请输入持卡人身份证号"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:layout_marginTop="8dp"
            android:inputType="numberSigned"
            android:digits="0123456789X"
            android:maxLength="18"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvUserIdCardTitle" />

        <ImageView
            android:id="@+id/ivUserIdCardClear"
            android:layout_width="@dimen/dimen_dp_44"
            android:layout_height="@dimen/dimen_dp_44"
            android:src="@drawable/clear_sousou"
            android:visibility="gone"
            android:padding="@dimen/dimen_dp_14"
            app:layout_constraintBottom_toBottomOf="@+id/etUserIdCard"
            app:layout_constraintEnd_toEndOf="@+id/etUserIdCard"
            app:layout_constraintTop_toTopOf="@+id/etUserIdCard"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvUserMobileTitle"
            style="@style/setPayPwText"
            android:layout_marginTop="@dimen/dimen_dp_15"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:text="预留手机号"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etUserIdCard" />

        <TextView
            android:id="@+id/tvUserMobileTitleTips"
            style="@style/setPayPwTextTips"
            android:layout_marginStart="@dimen/dimen_dp_5"
            app:layout_constraintBottom_toBottomOf="@+id/tvUserMobileTitle"
            app:layout_constraintStart_toEndOf="@+id/tvUserMobileTitle"
            app:layout_constraintTop_toTopOf="@+id/tvUserMobileTitle"
            tools:text="请输入正确的身份证号" />

        <EditText
            android:id="@+id/etUserMobile"
            style="@style/setPayPwEdit"
            android:hint="请输入预留手机号"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:layout_marginTop="8dp"
            android:inputType="number"
            android:digits="0123456789"
            android:maxLength="11"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvUserMobileTitle" />

        <ImageView
            android:id="@+id/ivUserMobileClear"
            android:layout_width="@dimen/dimen_dp_44"
            android:layout_height="@dimen/dimen_dp_44"
            android:src="@drawable/clear_sousou"
            android:visibility="gone"
            android:padding="@dimen/dimen_dp_14"
            app:layout_constraintBottom_toBottomOf="@+id/etUserMobile"
            app:layout_constraintEnd_toEndOf="@+id/etUserMobile"
            app:layout_constraintTop_toTopOf="@+id/etUserMobile"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvPrivacy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:textSize="@dimen/dimen_dp_12"
            android:layout_marginTop="@dimen/dimen_dp_15"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/etUserMobile"
            tools:text="请仔细阅读，支付服务相关协议" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/rtvNext"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_44"
            android:layout_marginStart="@dimen/dimen_dp_10"
            android:layout_marginEnd="@dimen/dimen_dp_10"
            android:text="同意协议并继续"
            android:layout_marginTop="@dimen/dimen_dp_25"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvPrivacy"
            app:rv_backgroundColor="@color/color_00b377"
            app:rv_cornerRadius="@dimen/dimen_dp_2" />
    </com.ybmmarket20.common.widget.RoundConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>