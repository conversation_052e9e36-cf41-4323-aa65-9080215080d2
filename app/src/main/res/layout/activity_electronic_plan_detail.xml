<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--android:id="@+id/ll_title" id不要随便换BaseActivity会用到-->
    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="@dimen/header_height"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="3dp"
        android:paddingTop="@dimen/header_height_padding_top">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="54dp"
            android:layout_height="25dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_back" />

        <RelativeLayout
            android:id="@+id/rel_search"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginBottom="4dp"
            android:layout_marginTop="4dp"
            android:layout_weight="1"
            android:background="@drawable/search_round_corner_gray_bg_03">

            <ImageView
                android:id="@+id/iv_a_magnifying_glass"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="4dp"
                android:padding="2dp"
                android:src="@drawable/icon_a_magnifying_glass" />

            <EditText
                android:id="@+id/title_et"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_marginLeft="34dp"
                android:layout_toLeftOf="@+id/iv_clear"
                android:background="@null"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:hint="@string/search_hint"
                android:imeOptions="actionSearch"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="#adaba8"
                android:textCursorDrawable="@drawable/color_cursor"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/iv_clear"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@drawable/clear_sousou"
                android:visibility="invisible" />

            <ImageView
                android:id="@+id/iv_voice"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@drawable/nav_voice_01"
                android:visibility="visible" />
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="54dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical">

            <RelativeLayout
                android:id="@+id/rl_cart"
                android:layout_width="54dp"
                android:layout_height="match_parent"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp">

                <ImageView
                    android:id="@+id/iv_cart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:src="@drawable/cart_icon" />

                <TextView
                    android:id="@+id/tv_num"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="2dp"
                    android:layout_marginTop="6dp"
                    android:background="@drawable/bg_message"
                    android:gravity="center"
                    android:text=""
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    android:visibility="gone" />
            </RelativeLayout>

        </RelativeLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <FrameLayout
            android:id="@+id/fl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
        <!--历史搜索记录-->
        <LinearLayout
            android:id="@+id/ll_history"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="35dp"
                    android:layout_marginTop="6dp"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="16dp"
                    android:text="历史搜索"
                    android:textColor="@color/text_676773"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_history"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="14dp"
                    android:layout_marginTop="6dp"
                    android:drawableLeft="@drawable/icon_clean"
                    android:gravity="center_horizontal|right"
                    android:paddingLeft="6dp"
                    android:paddingRight="6dp"
                    android:text="" />
            </LinearLayout>

            <com.ybm.app.view.CommonRecyclerView
                android:id="@+id/rcv_history"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingLeft="16dp"
                android:paddingRight="16dp" />
        </LinearLayout>
    </FrameLayout>
</LinearLayout>


