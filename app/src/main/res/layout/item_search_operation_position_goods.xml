<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingBottom="@dimen/dimen_dp_6"
    tools:layout_width="@dimen/dimen_dp_110">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_op_icon_bg"
        app:layout_constraintTop_toTopOf="parent" />

    <com.ybmmarket20.common.widget.RoundConstraintLayout
        android:id="@+id/clTop"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_94"
        app:rv_backgroundColor="#FBFBFB"
        app:rv_cornerRadius="@dimen/dimen_dp_8"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/ivOPGoods"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/dimen_dp_5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/logo" />

        <View
            android:id="@+id/vOPIconMark"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/shape_op_icon_mark"
            android:visibility="gone"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clOPTime"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_18"
            android:paddingEnd="@dimen/dimen_dp_2"
            android:background="@drawable/shape_op_time_bg_seckill"
            app:layout_constraintBottom_toBottomOf="@+id/vOPIconMark"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_weight="1"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/tvOPGoodsTimeTitle"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/shape_op_time_title_seckill"
                android:gravity="center"
                android:paddingStart="@dimen/dimen_dp_3"
                android:paddingEnd="@dimen/dimen_dp_3"
                android:text="距开始"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_10"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Space
                android:id="@+id/spaceTime"
                android:layout_width="@dimen/dimen_dp_2"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvOPGoodsTimeTitle"
                app:layout_constraintEnd_toStartOf="@+id/tvOPGoodsTimeHour"/>

            <TextView
                android:id="@+id/tvOPGoodsTimeHour"
                android:layout_width="@dimen/dimen_dp_13"
                android:layout_height="13dp"
                android:background="@drawable/shape_op_time_item_seckill"
                android:gravity="center"
                android:text="03"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_9"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvOPGoodsSymbol1"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toEndOf="@+id/spaceTime"
                app:layout_constraintTop_toTopOf="parent"
                app:rv_backgroundColor="#EA0000"
                app:rv_cornerRadius="@dimen/dimen_dp_2" />

            <TextView
                android:id="@+id/tvOPGoodsSymbol1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_dp_3"
                android:layout_marginEnd="@dimen/dimen_dp_3"
                android:text=":"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_9"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvOPGoodsTimeMinute"
                app:layout_constraintStart_toEndOf="@+id/tvOPGoodsTimeHour"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tvOPGoodsTimeMinute"
                android:layout_width="@dimen/dimen_dp_13"
                android:layout_height="13dp"
                android:background="@drawable/shape_op_time_item_seckill"
                android:gravity="center"
                android:text="12"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_9"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvOPGoodsSymbol2"
                app:layout_constraintStart_toEndOf="@+id/tvOPGoodsSymbol1"
                app:layout_constraintTop_toTopOf="parent"
                app:rv_cornerRadius="@dimen/dimen_dp_2" />

            <TextView
                android:id="@+id/tvOPGoodsSymbol2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dimen_dp_3"
                android:layout_marginEnd="@dimen/dimen_dp_3"
                android:text=":"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_9"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/tvOPGoodsTimeSecond"
                app:layout_constraintStart_toEndOf="@+id/tvOPGoodsTimeMinute"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvOPGoodsTimeSecond"
                android:layout_width="@dimen/dimen_dp_13"
                android:layout_height="13dp"
                android:background="@drawable/shape_op_time_item_seckill"
                android:gravity="center"
                android:text="56"
                android:textColor="@color/white"
                android:textSize="@dimen/dimen_dp_9"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvOPGoodsSymbol2"
                app:layout_constraintTop_toTopOf="parent"
                app:rv_cornerRadius="@dimen/dimen_dp_2" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/tvOPGoodsSellOut"
            android:layout_width="@dimen/dimen_dp_44"
            android:layout_height="@dimen/dimen_dp_44"
            android:src="@drawable/icon_operation_position_goods_sell_out"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivOPGoods"
            app:layout_constraintEnd_toEndOf="@+id/ivOPGoods"
            app:layout_constraintStart_toStartOf="@+id/ivOPGoods"
            app:layout_constraintTop_toTopOf="@+id/ivOPGoods" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tvOPGoodsOffShelf"
            android:layout_width="@dimen/dimen_dp_44"
            android:layout_height="@dimen/dimen_dp_44"
            android:gravity="center"
            android:text="下架"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_13"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/ivOPGoods"
            app:layout_constraintEnd_toEndOf="@+id/ivOPGoods"
            app:layout_constraintStart_toStartOf="@+id/ivOPGoods"
            app:layout_constraintTop_toTopOf="@+id/ivOPGoods"
            app:rv_backgroundColor="#DF4A4A4A"
            app:rv_cornerRadius="@dimen/dimen_dp_22" />
    </com.ybmmarket20.common.widget.RoundConstraintLayout>

    <TextView
        android:id="@+id/tvOPGoodsTitle"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dimen_dp_40"
        android:layout_marginStart="@dimen/dimen_dp_2"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="15盒包邮 丽芙甲硝唑凝胶/50mg"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_13"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/clTop"
        app:layout_constraintStart_toStartOf="@+id/clTop"
        app:layout_constraintTop_toBottomOf="@+id/clTop" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvOPGoodsTitle"
        app:layout_constraintVertical_weight="1">

        <TextView
            android:id="@+id/tvOPGoodsPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_4"
            android:text="¥99.99"
            android:textColor="@color/color_ff2121"
            android:textSize="@dimen/dimen_dp_18"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/ivOPGoodsBtnSpellGroup"
            android:layout_width="@dimen/dimen_dp_34"
            android:layout_height="@dimen/dimen_dp_23"
            android:src="@drawable/icon_operation_position_goods_btn_spell_group"
            app:layout_constraintBottom_toBottomOf="@id/tvOPGoodsPrice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvOPGoodsPrice" />

        <ImageView
            android:id="@+id/ivOPGoodsBtnPigou"
            android:layout_width="@dimen/dimen_dp_34"
            android:layout_height="@dimen/dimen_dp_23"
            android:src="@drawable/icon_operation_position_goods_btn_pigou"
            app:layout_constraintBottom_toBottomOf="@id/tvOPGoodsPrice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvOPGoodsPrice"
            tools:layout_marginTop="@dimen/dimen_dp_50" />

        <ImageView
            android:id="@+id/ivOPGoodsBtnPreHot"
            android:layout_width="@dimen/dimen_dp_54"
            android:layout_height="@dimen/dimen_dp_23"
            android:src="@drawable/icon_operation_position_goods_btn_prehot"
            app:layout_constraintBottom_toBottomOf="@id/tvOPGoodsPrice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvOPGoodsPrice"
            tools:layout_marginTop="@dimen/dimen_dp_100" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/ivOPGoodsBtnPigouSellOut"
            android:layout_width="@dimen/dimen_dp_34"
            android:layout_height="@dimen/dimen_dp_23"
            android:gravity="center"
            android:text="抢光"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_12"
            app:layout_constraintBottom_toBottomOf="@id/tvOPGoodsPrice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvOPGoodsPrice"
            app:rv_backgroundColor="#AAACB9"
            app:rv_cornerRadius="@dimen/dimen_dp_4"
            tools:layout_marginTop="@dimen/dimen_dp_150" />

        <com.ybmmarket20.view.ProductEditLayoutNew
            android:id="@+id/tvOPGoodsPel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/tvOPGoodsPrice"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvOPGoodsPrice"
            tools:layout_marginTop="@dimen/dimen_dp_200"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvOPGoodsPriceControlled"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_7"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="认证资质可见"
            android:textColor="#FF6204"
            android:textSize="@dimen/dimen_dp_15"
            android:textStyle="bold"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_marginTop="@dimen/dimen_dp_135" />

        <TextView
            android:id="@+id/tvOPGoodsNoPurchaseProtocol"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_7"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="暂无购买权限"
            android:textColor="#FF6204"
            android:textSize="@dimen/dimen_dp_15"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_marginTop="@dimen/dimen_dp_160" />

        <TextView
            android:id="@+id/tvOPGoodsShop"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_dp_32"
            android:drawableStart="@drawable/icon_list_item_shop"
            android:drawableEnd="@drawable/icon_arrow_right_gray"
            android:drawablePadding="@dimen/dimen_dp_2"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="湖北小药药自营旗舰店"
            android:textColor="@color/color_676773"
            android:textSize="@dimen/dimen_dp_11"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_marginTop="@dimen/dimen_dp_180" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>