<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:paddingBottom="15dp"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/iv_goods"
        android:layout_width="@dimen/dimen_dp_70"
        android:layout_height="@dimen/dimen_dp_70"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginBottom="@dimen/dimen_dp_15"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_goods_title"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_14"
        android:textColor="@color/invoice_tv_292933"
        android:maxLines="2"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="10dp"
        android:ellipsize="end"
        android:text="念慈菴 蜜炼川贝枇杷膏/500ml/盒"
        android:layout_marginStart="@dimen/dimen_dp_20"
        app:layout_constraintStart_toEndOf="@+id/iv_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_goods" />

    <TextView
        android:id="@+id/tv_manufactor"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_676773"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginRight="10dp"
        android:text="施慧达药业集团(吉林)科技有限公司"
        android:layout_marginTop="@dimen/dimen_dp_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_goods_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_goods_title" />

    <TextView
        android:id="@+id/tv_effect"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dimen_dp_12"
        android:textColor="@color/color_676773"
        android:maxLines="1"
        android:ellipsize="end"
        android:text="有效期：2025.10.16"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_goods_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_manufactor" />


    <TextView
        android:id="@+id/tv_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_dp_5"
        android:text="¥24.00/盒"
        android:textColor="@color/color_ff2121"
        android:textSize="20sp"
        app:layout_constraintStart_toStartOf="@+id/tv_goods_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_effect" />

    <com.ybmmarket20.view.ProductEditLayoutSuiXinPin
        android:id="@+id/pel"
        android:layout_width="@dimen/dimen_dp_25"
        android:layout_height="@dimen/dimen_dp_25"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_effect" />

    <ImageView
        android:id="@+id/iv_goods_tag"
        android:layout_width="@dimen/pic_goods_list_little"
        android:layout_height="@dimen/pic_goods_list_little"
        app:layout_constraintStart_toStartOf="@+id/iv_goods"
        app:layout_constraintTop_toTopOf="@+id/iv_goods" />

    <TextView
        android:id="@+id/tv_settle_out"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:background="@drawable/bg_goods_sold_out"
        android:gravity="center"
        android:textColor="@color/color_fdfdfd"
        android:textSize="@dimen/dimen_dp_13"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/iv_goods"
        app:layout_constraintLeft_toLeftOf="@id/iv_goods"
        app:layout_constraintRight_toRightOf="@id/iv_goods"
        app:layout_constraintTop_toTopOf="@id/iv_goods"
        android:text="售罄"
        tools:text="售罄"
        tools:visibility="invisible" />

    <!-- 订阅-->
    <LinearLayout
        android:id="@+id/ll_subscribe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/tv_effect"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_goods_subscribe"
            android:layout_width="@dimen/dimen_dp_20"
            android:layout_height="@dimen/dimen_dp_20"
            android:layout_gravity="center_horizontal"
            android:visibility="visible"
            tools:src="@drawable/icon_goods_subscribe"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_goods_subscribe"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_292933"
            android:textSize="@dimen/dimen_dp_10"
            android:visibility="visible"
            tools:text="到货通知"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>