<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/audience_play_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/icon_tv_live_default_bg"
    tools:ignore="contentDescription">



    <com.ybmmarket20.view.liveview.EmptyControlVideo
        android:id="@+id/video_player"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <RelativeLayout
        android:id="@+id/anchor_rl_controllLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        # 直播间头部信息
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/icon_tv_live_top_default_bg">

            <include
                android:id="@+id/layout_live_pusher_info"
                layout="@layout/layout_live_pusher_info"
                android:layout_width="wrap_content"
                android:layout_marginTop="30dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp" />

            <Button
                android:id="@+id/btn_back"
                android:layout_width="@dimen/live_btn_size"
                android:layout_height="@dimen/live_btn_size"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="30dp"
                android:layout_marginEnd="12.5dp"
                android:background="@drawable/btn_close"
                android:onClick="onClick" />

            <ImageView
                android:id="@+id/iv_coupon_bag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/btn_back"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="10dp"
                android:background="@drawable/icon_tv_live_coupon_bag"
                android:onClick="onClick" />
        </RelativeLayout>

        # 直播间底部信息
        <FrameLayout
            android:id="@+id/tool_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@drawable/icon_tv_live_bottom_default_bg">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_margin="15dp"
                android:orientation="horizontal">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:id="@+id/iv_shopping_bag"
                        android:layout_width="@dimen/live_btn_size"
                        android:layout_height="@dimen/live_btn_size"
                        android:background="@drawable/icon_tv_live_shopping_bag"
                        android:onClick="onClick" />
                    <TextView
                        android:id="@+id/tv_smg_num"
                        style="@style/more_msg_tip_style"
                        android:layout_alignParentRight="false"
                        android:layout_toEndOf="@+id/iv_shopping_bag"
                        android:layout_marginStart="-10dp"
                        android:visibility="gone"
                        tools:text="9"
                        tools:visibility="visible" />
                </RelativeLayout>

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    android:layout_weight="1">

                    <TextView
                        android:id="@+id/btn_message_input"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dimen_dp_36"
                        android:background="#66000000"
                        android:gravity="center_vertical"
                        android:onClick="onClick"
                        android:paddingStart="10dp"
                        android:text="@string/str_tv_live_edit_hint"
                        android:textColor="@color/colorTextWhite"
                        android:textSize="14dp" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/btn_like"
                        android:layout_width="@dimen/live_btn_size"
                        android:layout_height="@dimen/live_btn_size"
                        android:layout_gravity="right"
                        android:background="@drawable/icon_tv_live_like"
                        android:onClick="onClick" />
                </FrameLayout>

                <ImageView
                    android:id="@+id/btn_share"
                    android:layout_marginLeft="15dp"
                    android:layout_width="@dimen/live_btn_size"
                    android:layout_height="@dimen/live_btn_size"
                    android:layout_gravity="right"
                    android:background="@drawable/icon_share"
                    android:onClick="onClick" />


            </LinearLayout>

        </FrameLayout>

        <ListView
            android:id="@+id/im_msg_listview"
            android:layout_width="250dp"
            android:layout_height="10dp"
            android:layout_alignParentStart="true"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="15dp"
            android:layout_marginBottom="70dp"
            android:cacheColorHint="#00000000"
            android:divider="#3c421b1b"
            android:listSelector="@android:color/transparent"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbars="none"
            android:stackFromBottom="true"
            android:transcriptMode="normal"/>

        <!--点赞动画-->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginRight="0dp"
            android:layout_marginBottom="10dp">

            <com.ybmmarket20.widget.like.TCHeartLayout
                android:id="@+id/heart_layout"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentBottom="true"
                android:focusable="true" />
        </RelativeLayout>

        <FrameLayout
            android:id="@+id/fl_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone"
            tools:visibility="visible">

            <RelativeLayout
                android:id="@+id/rl_reload"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tv_reload_error"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="加载失败,请刷新重试"
                    android:textColor="@color/colorTextWhite"
                    android:textSize="18dp"
                    android:textStyle="bold" />

                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/tv_reload"
                    android:layout_width="72dp"
                    android:layout_height="30dp"
                    android:layout_below="@+id/tv_reload_error"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="15dp"
                    android:gravity="center"
                    android:text="刷新"
                    android:textColor="@color/colorTextWhite"
                    android:textSize="12dp"
                    app:rv_cornerRadius="15dp"
                    app:rv_strokeColor="@color/colorTextWhite"
                    app:rv_strokeWidth="1dp" />
            </RelativeLayout>


            <TextView
                android:id="@+id/tv_wait_over"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:drawableTop="@drawable/icon_tv_live_over"
                android:drawablePadding="22.5dp"
                android:textColor="@color/colorTextWhite"
                android:textSize="18dp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="gone"
                tools:text="直播已结束，稍后可查看视频回放" />

        </FrameLayout>
    </RelativeLayout>

    <LinearLayout
        android:layout_marginTop="100dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_enter_room"
            android:layout_width="100dp"
            android:layout_height="44dp"
            android:background="@color/color_4D000000"
            android:onClick="onClick"
            android:gravity="center"
            android:text="进入房间" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_content"
                android:layout_width="100dp"
                android:layout_height="44dp" />

            <TextView
                android:id="@+id/tv_sendText"
                android:layout_width="100dp"
                android:onClick="onClick"
                android:layout_height="44dp"
                android:background="@color/color_4D000000"
                android:text="发送消息" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_custom_msg"
            android:layout_width="100dp"
            android:text="发送自定义群消息"
            android:gravity="center"
            android:background="@color/color_4D000000"
            android:layout_height="44dp"/>

    </LinearLayout>
</FrameLayout>