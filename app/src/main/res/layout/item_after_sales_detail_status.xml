<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/dimen_dp_10"
    android:background="@color/white">

    <TextView
        android:id="@+id/tvStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="等待客户退回发票"
        android:textColor="@color/color_292933"
        android:textSize="@dimen/dimen_dp_15"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_15"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/llRemainTime"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:background="@drawable/bg_brand_f95e35"
        android:orientation="horizontal"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        app:layout_constraintBottom_toBottomOf="@+id/tvStatus"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvStatus">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/icon_order_unpay"
            android:paddingStart="@dimen/dimen_dp_3"
            android:paddingEnd="@dimen/dimen_dp_5"
            android:gravity="center"
            android:text="剩余时间"
            android:textColor="@color/white"
            android:textSize="10dp" />

        <TextView
            android:id="@+id/tvRemainTimeText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:textColor="@color/color_ff5729"
            android:textSize="11dp"
            tools:text="1天22小时56分" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tvStatus">

        <ImageView
            android:id="@+id/statusLeft"
            android:layout_width="@dimen/dimen_dp_9"
            android:layout_height="@dimen/dimen_dp_9"
            android:src="@drawable/icon_cart_bottom_coupon_checked"
            android:layout_marginStart="@dimen/dimen_dp_65"
            app:layout_constraintBottom_toBottomOf="@+id/ivStatusMiddle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/lineLeft"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivStatusMiddle" />

        <View
            android:id="@+id/lineLeft"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_2"
            android:background="@color/color_00b377"
            android:layout_margin="@dimen/dimen_dp_2"
            app:layout_constraintBottom_toBottomOf="@+id/ivStatusMiddle"
            app:layout_constraintEnd_toStartOf="@+id/ivStatusMiddle"
            app:layout_constraintStart_toEndOf="@+id/statusLeft"
            app:layout_constraintTop_toTopOf="@+id/ivStatusMiddle" />

        <ImageView
            android:id="@+id/ivStatusMiddle"
            android:layout_width="@dimen/dimen_dp_13"
            android:layout_height="@dimen/dimen_dp_13"
            android:src="@drawable/icon_refund_after_sales_status_middle"
            android:layout_marginTop="@dimen/dimen_dp_16"
            app:layout_constraintEnd_toStartOf="@+id/lineRight"
            app:layout_constraintStart_toEndOf="@+id/lineLeft"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/lineRight"
            android:layout_width="@dimen/dimen_dp_0"
            android:layout_height="@dimen/dimen_dp_2"
            android:background="@color/color_E6E6E6"
            android:layout_margin="@dimen/dimen_dp_2"
            app:layout_constraintBottom_toBottomOf="@+id/ivStatusMiddle"
            app:layout_constraintEnd_toStartOf="@+id/statusRight"
            app:layout_constraintStart_toEndOf="@+id/ivStatusMiddle"
            app:layout_constraintTop_toTopOf="@+id/ivStatusMiddle" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/statusRight"
            android:layout_width="@dimen/dimen_dp_9"
            android:layout_height="@dimen/dimen_dp_9"
            android:layout_marginEnd="@dimen/dimen_dp_65"
            app:layout_constraintBottom_toBottomOf="@+id/ivStatusMiddle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/lineRight"
            app:layout_constraintTop_toTopOf="@+id/ivStatusMiddle"
            app:rv_backgroundColor="@color/color_E6E6E6"
            app:rv_cornerRadius="@dimen/dimen_dp_4_5" />


        <TextView
            android:id="@+id/tvStatusLeftText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_292933"
            android:textSize="@dimen/dimen_dp_12"
            tools:text="等待商家处理"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="@+id/statusLeft"
            app:layout_constraintStart_toStartOf="@+id/statusLeft"
            app:layout_constraintTop_toBottomOf="@+id/statusLeft" />

        <TextView
            android:id="@+id/tvStatusMiddleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_00b377"
            android:textSize="@dimen/dimen_dp_12"
            tools:text="待客户退回发票"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="@+id/ivStatusMiddle"
            app:layout_constraintStart_toStartOf="@+id/ivStatusMiddle"
            app:layout_constraintTop_toBottomOf="@+id/ivStatusMiddle" />

        <TextView
            android:id="@+id/tvStatusRightText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_9494A6"
            android:textSize="@dimen/dimen_dp_12"
            tools:text="处理完成"
            android:layout_marginTop="@dimen/dimen_dp_10"
            app:layout_constraintEnd_toEndOf="@+id/statusRight"
            app:layout_constraintStart_toStartOf="@+id/statusRight"
            app:layout_constraintTop_toBottomOf="@+id/statusRight" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/rtvStatusTips"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/dimen_dp_10"
        android:paddingBottom="@dimen/dimen_dp_10"
        android:layout_marginTop="@dimen/dimen_dp_15"
        android:layout_marginStart="@dimen/dimen_dp_10"
        android:layout_marginEnd="@dimen/dimen_dp_10"
        android:text="您已撤回售后申请，如有需求，可再次发起。"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/dimen_dp_10"
        android:paddingEnd="@dimen/dimen_dp_10"
        android:textSize="@dimen/dimen_dp_13"
        android:textColor="@color/color_9494A6"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clStatus"
        app:rv_backgroundColor="@color/color_f7f7f8"
        app:rv_cornerRadius="@dimen/dimen_dp_2" />

</androidx.constraintlayout.widget.ConstraintLayout>