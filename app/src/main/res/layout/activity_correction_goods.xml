<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:padding="10dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/lableTips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/multi_correction_goods"
                android:textColor="#292933"
                android:textSize="14sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/lableRv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/lableTips" />

            <View
                android:id="@+id/divider4"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginTop="14dp"
                android:background="#eeeeee"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/lableRv" />

            <fragment
                android:id="@+id/commonCorrection"
                android:name="com.ybmmarket20.business.correction.ui.fragment.CommonCorrectionFragment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/divider4" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

</LinearLayout>