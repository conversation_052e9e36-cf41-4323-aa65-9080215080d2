<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/layout_load_error"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/icon_no_network"
        android:layout_marginTop="130dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_describe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="网络加载失败"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/imageView"
        android:textSize="16dp"
        android:textColor="@color/text_9494A6"/>

    <com.ybmmarket20.common.widget.RoundTextView
        android:id="@+id/tv_reload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="点击重新加载"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_describe"
        android:layout_marginTop="20dp"
        app:rv_cornerRadius="25dp"
        app:rv_strokeColor="#00B377"
        app:rv_strokeWidth="1dp"
        android:gravity="center"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        android:textSize="15dp"
        android:textColor="#00B377"/>
</androidx.constraintlayout.widget.ConstraintLayout>