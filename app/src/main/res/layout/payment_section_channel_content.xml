<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_payment_section_channel_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="10dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="10dp">

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/icon_cart_proprietary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="1.67dp"
            android:paddingRight="1.67dp"
            android:text="自营"
            android:textColor="#00B377"
            android:textSize="11sp"
            android:visibility="visible"
            app:rv_backgroundColor="#0D00B377"
            app:rv_cornerRadius="1.33dp"
            app:rv_strokeColor="#8000B377"
            app:rv_strokeWidth="0.5dp" />

        <TextView
            android:id="@+id/tv_cart_proprietary"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginLeft="6dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textColor="@color/cart_head_tv01"
            android:textSize="@dimen/cart_content_tv01"
            android:textStyle="bold"
            tools:text="江西小药药" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_product"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:paddingRight="12dp"
        android:paddingBottom="10dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical">

            <FrameLayout style="@style/payment_product">

                <ImageView
                    android:id="@+id/iv_product_1"
                    style="@style/payment_iv_product" />
            </FrameLayout>

            <FrameLayout style="@style/payment_product">

                <ImageView
                    android:id="@+id/iv_product_2"
                    style="@style/payment_iv_product" />

            </FrameLayout>

            <FrameLayout style="@style/payment_product">

                <ImageView
                    android:id="@+id/iv_product_3"
                    style="@style/payment_iv_product" />

            </FrameLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_product_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableRight="@drawable/common_more"
                android:drawablePadding="3dp"
                android:gravity="right"
                android:text="共0件"
                android:textColor="@color/text_9494A6"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_gift"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="3dp"
                android:text="含物料心愿单礼包"
                android:textColor="#F76A24"
                android:textSize="12sp"
                android:visibility="gone" />

        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_payment_num"
        style="@style/payment_item_layout"
        android:layout_marginTop="0dp"
        android:background="@null">

        <LinearLayout
            android:id="@+id/cart_new_ll"
            style="@style/payment_item_order_ll_key"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/cart_new_rl_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="1.67dp"
                android:paddingRight="1.67dp"
                android:visibility="visible" />

            <TextView
                android:id="@+id/cart_new_rl_tv01"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="7dp"
                android:gravity="center_vertical"
                android:maxLines="2"
                android:text=""
                android:textColor="@color/cart_head_tv01"
                android:textSize="13sp"/>

            <ImageView
                android:id="@+id/cart_attention"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:gravity="center_vertical"
                android:src="@drawable/icon_channel_attention" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_payment_price"
            style="@style/payment_item_order_text_value"
            android:textColor="@color/text_292933" />
    </LinearLayout>

</LinearLayout>