<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fillViewport="true"
    android:orientation="vertical">

    <include layout="@layout/common_header_items" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout style="@style/address_layout_style">

                <TextView
                    style="@style/address_title_style"
                    android:text="收货人"
                    android:textColor="@color/text_676773" />

                <com.ybmmarket20.view.EditTextWithDel
                    android:id="@+id/et_name"
                    style="@style/address_input_style"
                    android:hint="请输入姓名"
                    android:textStyle="bold" />
            </LinearLayout>

            <View style="@style/address_line" />

            <LinearLayout style="@style/address_layout_style">

                <TextView
                    style="@style/address_title_style"
                    android:text="电话号码"
                    android:textColor="@color/text_676773" />

                <com.ybmmarket20.view.EditTextWithDel
                    android:id="@+id/et_mobile"
                    style="@style/address_input_style"
                    android:hint="请输入手机号码"
                    android:inputType="phone"
                    android:textStyle="bold" />
            </LinearLayout>

            <View style="@style/address_line" />

            <LinearLayout
                style="@style/address_layout_style"
                android:layout_height="wrap_content"
                android:minHeight="48dp">

                <TextView
                    style="@style/address_title_style"
                    android:layout_gravity="center_vertical"
                    android:text="收货地址"
                    android:textColor="@color/text_676773" />

                <TextView
                    android:id="@+id/et_address"
                    style="@style/address_input_style"
                    android:ellipsize="end"
                    android:enabled="false"
                    android:hint="请输入配送地址"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:textColor="@color/color_292933" />
            </LinearLayout>

            <View
                android:id="@+id/view_line"
                android:visibility="gone"
                style="@style/address_line" />

            <LinearLayout
                android:id="@+id/ll_remark"
                android:visibility="gone"
                style="@style/address_layout_style">

                <TextView
                    style="@style/address_title_style"
                    android:layout_gravity="center_vertical"
                    android:text="地址备注"
                    android:textColor="@color/text_676773" />

                <TextView
                    android:id="@+id/et_remark"
                    style="@style/address_input_style"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:enabled="false"
                    android:hint="请输入备注地址"
                    android:maxLines="2"
                    android:singleLine="false"
                    android:textColor="@color/text_9494A6" />
            </LinearLayout>

            <com.ybmmarket20.common.widget.RoundLinearLayout
                android:id="@+id/ll_remark_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                app:rv_cornerRadius="1dp"
                app:rv_strokeWidth="0.33dp">

                <LinearLayout style="@style/address_layout_style">

                    <TextView
                        style="@style/address_title_style"
                        android:text="地址备注"
                        android:textColor="@color/text_676773" />

                    <com.ybmmarket20.view.EditTextWithDel
                        android:id="@+id/et_remark_input"
                        style="@style/address_input_style"
                        android:hint="请输入地址备注"
                        android:maxLength="50" />
                </LinearLayout>

                <com.ybmmarket20.common.widget.RoundTextView
                    android:id="@+id/tv_tips"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:layout_marginLeft="10dp"
                    android:layout_marginRight="10dp"
                    android:padding="10dp"
                    android:text="请确保地址能被快递员识别，如不能识别，请在输入框内加以辅助描述，限50字。地址备注信息不可重复修改，请认真填写。"
                    android:textColor="@color/colors_99664D"
                    android:textSize="12sp"
                    app:rv_backgroundColor="@color/colors_fff7ef"
                    app:rv_cornerRadius="2dp" />

            </com.ybmmarket20.common.widget.RoundLinearLayout>

            <View style="@style/address_line" />

            <RelativeLayout
                android:id="@+id/ll_set_check"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:background="@color/white"
                android:minHeight="44dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:paddingLeft="0dp"
                    android:text="设置为默认收货地址"
                    android:textColor="@color/color_292933"
                    android:textSize="15sp" />

                <CheckBox
                    android:id="@+id/cb_setting"
                    style="@style/addressCheckboxTheme_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:clickable="false" />
            </RelativeLayout>

            <TextView
                android:id="@+id/tv_show_default"
                style="@style/address_layout_style"
                android:gravity="center_vertical"
                android:text="默认收货地址"
                android:textColor="@color/colors_9595A6"
                android:textSize="15sp"
                android:visibility="gone" />

        </LinearLayout>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/btn_ok"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_below="@+id/ll"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginTop="30dp"
            android:gravity="center"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:rv_backgroundColor="@color/base_colors_new"
            app:rv_cornerRadius="2dp" />

    </RelativeLayout>
</LinearLayout>