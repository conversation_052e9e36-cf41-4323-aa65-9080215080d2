package com.ybmmarketkotlin.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.xyyreport.page.search.GoodsPlaceExposureRecord
import com.ybmmarket20.xyyreport.page.search.SearchProductReport

/**
 * 加价购组合购埋点
 */
open class GoodsCombinedBuyItemAnalysisAdapter(layout: Int, products: MutableList<RowsBeanCombinedExt>?) :
    YBMBaseListAdapter<RowsBeanCombinedExt>(layout, products) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder, product: RowsBeanCombinedExt) {
        val goodsPlaceExposureRecord = GoodsPlaceExposureRecord.get(mContext)
        val recordKey = product.getGroupGoodsPlaceInfo()?.getRecordKey(mContext)
        if (!goodsPlaceExposureRecord.containsRecord(recordKey)) {
            goodsPlaceExposureRecord.markRecord(recordKey)
            SearchProductReport.trackSearchGoodsExposure(mContext, product, baseViewHolder.bindingAdapterPosition)
        }
    }
}