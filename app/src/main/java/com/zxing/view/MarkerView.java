package com.zxing.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.view.View;

import com.google.zxing.ResultPoint;
import com.ybmmarket20.R;
import com.ybmmarket20.common.util.ConvertUtils;

/**
 * Created by ybm on 2017/6/26.
 * 扫描页面上面的扫描框的样式
 */

public class MarkerView extends View {

    private static final int LASER_MARGIN = 20;//扫描线到左右距离
    private static final int CORNER_MARGIN = 4;//四个角到框的间距
    private static final int FRAME_CORNER_LENGTH = 30;//扫描框4角高
    private static final int FRAME_CORNER_WIDTH = 8;//扫描框4角宽
    private static final int DRAW_DELAY = 18;//重绘间隔的时间
    private static final int FRAME_CHANGE_SPEED = 12;//框的下边缘每次移动的距离

    private Paint mPaint;
    public Rect mRectFrame;//扫描框的范围
    private Rect mLaserRect;//扫描线的绘制范围
    private Bitmap mLaserBitmap;//扫描线
    private int mLaserLineHeight = 2;//扫描线的高度
    private int mLaserLineTop;//扫描线距离顶部的距离
    private int mTargetViewHeight = 0;//搜索框的高度
    private int mFrameChangeTotalHeight = 0;//框到底部的距离
    private boolean mNeedChange;//扫描框是否变化
    private boolean isExpand;//从输入框变成扫描框
    private int mCanvasW, mCanvasH;//画布的宽高

    private int mLeft1, mLeft2, mLeft3;//左边3个x值
    private int mRight1, mRight2, mRight3;//右边3个x值
    private int mTop1, mTop2, mTop3;//上边3个y值
    private int mBottom1, mBottom2, mBottom3;//下边3个y值


    private ChangeListener mListener;

    public MarkerView(Context context) {
        this(context, null);
    }

    public MarkerView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.FILL);
        DisplayMetrics dm = getResources().getDisplayMetrics();
        int width = dm.widthPixels;
        int height = dm.heightPixels;
        int w = ConvertUtils.dp2px(280);
        int h = ConvertUtils.dp2px(180);
        mRectFrame = new Rect((width - w) / 2, (height - h) / 2, (width + w) / 2, (height + h) / 2);
        mLaserLineTop = mRectFrame.top;
        initFrame();
    }

    private void initFrame() {
        mLeft1 = mRectFrame.left - FRAME_CORNER_WIDTH - CORNER_MARGIN;
        mLeft2 = mLeft1 + FRAME_CORNER_WIDTH;
        mLeft3 = mLeft1 + FRAME_CORNER_LENGTH;
        mRight1 = mRectFrame.right + CORNER_MARGIN - FRAME_CORNER_LENGTH;
        mRight2 = mRectFrame.right + CORNER_MARGIN;
        mRight3 = mRight2 + FRAME_CORNER_WIDTH;
        mTop1 = mRectFrame.top - CORNER_MARGIN - FRAME_CORNER_WIDTH;
        mTop2 = mTop1 + FRAME_CORNER_WIDTH;
        mTop3 = mTop1 + FRAME_CORNER_LENGTH;
        mBottom1 = mRectFrame.bottom + CORNER_MARGIN - FRAME_CORNER_LENGTH;
        mBottom2 = mRectFrame.bottom + CORNER_MARGIN;
        mBottom3 = mBottom2 + FRAME_CORNER_WIDTH;
    }

    public void setChangeListener(ChangeListener listener) {
        mListener = listener;
    }

    /**
     * 改变框
     *
     * @param height
     * @param expand
     */
    public void changeStyle(int height, boolean expand) {
        if (mRectFrame == null) {
            return;
        }
        mLaserLineTop = mRectFrame.top;
        mTargetViewHeight = height;
        mNeedChange = true;
        isExpand = expand;
        if (expand) {
            mFrameChangeTotalHeight = mRectFrame.bottom
                    - mRectFrame.top - mTargetViewHeight;
            invalidate();
        } else {
            mFrameChangeTotalHeight = 0;
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        drawMask(canvas, mRectFrame);
        drawFrameCorner(canvas, mRectFrame);
        if (!mNeedChange) {
            drawLaserLine(canvas, mRectFrame);
            moveLaserSpeed(mRectFrame);
        } else {
            if (isExpand) {
                expandFrameAnim(mRectFrame);
            } else {
                contractFrameAnim(mRectFrame);
            }
        }
    }

    /**
     * 半透明遮盖
     *
     * @param canvas
     * @param frame
     */
    private void drawMask(Canvas canvas, Rect frame) {
        if (mCanvasW == 0 || mCanvasH == 0) {
            mCanvasW = canvas.getWidth();
            mCanvasH = canvas.getHeight();
        }
        mPaint.setColor(0x99000000);

        canvas.drawRect(0, 0, frame.left, mCanvasH, mPaint);
        canvas.drawRect(frame.left, 0, frame.right, frame.top, mPaint);
        canvas.drawRect(frame.right, 0, mCanvasW, mCanvasH, mPaint);
        canvas.drawRect(frame.left, frame.bottom - mFrameChangeTotalHeight, frame.right, mCanvasH, mPaint);
    }

    /**
     * 绘制扫描框区域
     */
    private void drawFrameCorner(Canvas canvas, Rect frame) {
        mPaint.setColor(Color.WHITE);
        if (mLeft1 == 0 || mLeft1 == mRight3 || mTop1 == mBottom3) {
            initFrame();
        }
        // 左上角(竖，横)
        canvas.drawRect(mLeft1, mTop1, mLeft2, mTop3, mPaint);
        canvas.drawRect(mLeft2, mTop1, mLeft3, mTop2, mPaint);
        // 右上角(竖，横)
        canvas.drawRect(mRight2, mTop1, mRight3, mTop3, mPaint);
        canvas.drawRect(mRight1, mTop1, mRight2, mTop2, mPaint);
        // 左下角(竖，横)
        canvas.drawRect(mLeft1, mBottom1 - mFrameChangeTotalHeight,
                mLeft2, mBottom3 - mFrameChangeTotalHeight, mPaint);
        canvas.drawRect(mLeft2, mBottom2 - mFrameChangeTotalHeight,
                mLeft3, mBottom3 - mFrameChangeTotalHeight, mPaint);
        // 右下角(竖，横)
        canvas.drawRect(mRight2, mBottom1 - mFrameChangeTotalHeight,
                mRight3, mBottom3 - mFrameChangeTotalHeight, mPaint);
        canvas.drawRect(mRight1, mBottom2 - mFrameChangeTotalHeight,
                mRight2, mBottom3 - mFrameChangeTotalHeight, mPaint);

//        // 左上角(竖，横)
//        canvas.drawRect(frame.ic_back - FRAME_CORNER_WIDTH - CORNER_MARGIN, frame.top - CORNER_MARGIN, frame.ic_back - CORNER_MARGIN, frame.top
//                - CORNER_MARGIN + FRAME_CORNER_LENGTH, mPaint);
//        canvas.drawRect(frame.left - FRAME_CORNER_WIDTH - CORNER_MARGIN, frame.top - FRAME_CORNER_WIDTH - CORNER_MARGIN
//                , frame.left + FRAME_CORNER_LENGTH - CORNER_MARGIN, frame.top - CORNER_MARGIN, mPaint);
//        // 右上角(竖，横)
//        canvas.drawRect(frame.right + CORNER_MARGIN, frame.top - CORNER_MARGIN, frame.right + CORNER_MARGIN + FRAME_CORNER_WIDTH,
//                frame.top - CORNER_MARGIN + FRAME_CORNER_LENGTH, mPaint);
//        canvas.drawRect(frame.right + CORNER_MARGIN - FRAME_CORNER_LENGTH, frame.top - CORNER_MARGIN - FRAME_CORNER_WIDTH,
//                frame.right + CORNER_MARGIN + FRAME_CORNER_WIDTH, frame.top - CORNER_MARGIN, mPaint);
//        // 左下角(竖，横)
//        canvas.drawRect(frame.left - CORNER_MARGIN - FRAME_CORNER_WIDTH, frame.bottom - CORNER_MARGIN - FRAME_CORNER_LENGTH - mFrameChangeTotalHeight,
//                frame.left - CORNER_MARGIN, frame.bottom + CORNER_MARGIN - mFrameChangeTotalHeight, mPaint);
//        canvas.drawRect(frame.ic_back - CORNER_MARGIN - FRAME_CORNER_WIDTH, frame.bottom + CORNER_MARGIN - mFrameChangeTotalHeight, frame.ic_back
//                - CORNER_MARGIN + FRAME_CORNER_LENGTH, frame.bottom + CORNER_MARGIN + FRAME_CORNER_WIDTH - mFrameChangeTotalHeight, mPaint);
//        // 右下角(竖，横)
//        canvas.drawRect(frame.right + CORNER_MARGIN, frame.bottom + CORNER_MARGIN - FRAME_CORNER_LENGTH - mFrameChangeTotalHeight, frame.right
//                + CORNER_MARGIN + FRAME_CORNER_WIDTH, frame.bottom + CORNER_MARGIN - mFrameChangeTotalHeight, mPaint);
//        canvas.drawRect(frame.right + CORNER_MARGIN - FRAME_CORNER_LENGTH, frame.bottom + CORNER_MARGIN - mFrameChangeTotalHeight, frame.right
//                + CORNER_MARGIN + FRAME_CORNER_WIDTH, frame.bottom + CORNER_MARGIN + FRAME_CORNER_WIDTH - mFrameChangeTotalHeight, mPaint);
    }

    /**
     * 画扫描线
     *
     * @param canvas
     * @param frame
     */
    private void drawLaserLine(Canvas canvas, Rect frame) {
        if (mLaserBitmap == null || mLaserBitmap.isRecycled()) {
            mLaserBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.icon_scan_laser);
            mLaserLineHeight = mLaserBitmap.getHeight();
        }
        if (mLaserBitmap != null) {
            if (mLaserRect == null) {
                mLaserRect = new Rect();
                mLaserRect.left = frame.left + LASER_MARGIN;
                mLaserRect.right = frame.right - LASER_MARGIN;
            }
            mLaserRect.top = mLaserLineTop;
            mLaserRect.bottom = mLaserLineTop + mLaserLineHeight;
            canvas.drawBitmap(mLaserBitmap, null, mLaserRect, mPaint);
        } else {
            mPaint.setColor(0x00B377);// 设置扫描线颜色
            canvas.drawRect(frame.left + LASER_MARGIN, mLaserLineTop, frame.right - LASER_MARGIN
                    , mLaserLineTop + mLaserLineHeight, mPaint);
        }

    }

    private void moveLaserSpeed(Rect frame) {
        // 每次刷新界面，扫描线往下移动(6为速度)
        mLaserLineTop += 10;
        if (mLaserLineTop + mLaserLineHeight >= frame.bottom) {
            mLaserLineTop = frame.top;
        }
        // 只刷新扫描框的内容，其他地方不刷新
        postInvalidateDelayed(DRAW_DELAY, frame.left + LASER_MARGIN, frame.top + LASER_MARGIN
                , frame.right - LASER_MARGIN, frame.bottom - LASER_MARGIN);
    }

    /**
     * 收起扫描框变成搜索框的样子
     *
     * @param frame
     */
    private void contractFrameAnim(Rect frame) {
        if (mTargetViewHeight == 0) {
            mTargetViewHeight = ConvertUtils.dp2px(50);
        }
        if (mFrameChangeTotalHeight == frame.bottom - frame.top - mTargetViewHeight) {
            return;
        }
        if (mListener != null) {
            mListener.start(false);
        }
        mFrameChangeTotalHeight += FRAME_CHANGE_SPEED;
        if (frame.bottom - frame.top - mFrameChangeTotalHeight < mTargetViewHeight) {
            mFrameChangeTotalHeight = frame.bottom - frame.top - mTargetViewHeight;
        }

        if (mFrameChangeTotalHeight <= frame.bottom - frame.top - mTargetViewHeight) {
            postInvalidateDelayed(3, frame.left, frame.top
                    , frame.right, frame.bottom);
            if (mFrameChangeTotalHeight == frame.bottom - frame.top - mTargetViewHeight) {
                if (mListener != null) {
                    mListener.end(false);
                }
            }
        }
    }

    /**
     * 展开搜索框变成扫描框
     *
     * @param frame
     */
    private void expandFrameAnim(Rect frame) {
        if (mTargetViewHeight == 0) {
            mTargetViewHeight = FRAME_CORNER_LENGTH * 4;
        }
        if (mFrameChangeTotalHeight == 0) {
            return;
        }
        mFrameChangeTotalHeight -= FRAME_CHANGE_SPEED;
        if (mFrameChangeTotalHeight < 0) {
            mFrameChangeTotalHeight = 0;
        }
        if (mListener != null) {
            mListener.start(true);
        }
        if (mFrameChangeTotalHeight >= 0) {
            if (mFrameChangeTotalHeight == 0) {
//                DRAW_DELAY = 0;
                mNeedChange = false;
            }
            postInvalidateDelayed(4, frame.left, frame.top
                    , frame.right, frame.bottom);
            if (mFrameChangeTotalHeight == 0) {
                if (mListener != null) {
                    mListener.end(true);
                }
            }
        }
    }

    public void recycleLaser() {
        if (mLaserBitmap != null && !mLaserBitmap.isRecycled()) {
            mLaserBitmap.recycle();
            mLaserBitmap = null;
        }
    }

    public void addPossibleResultPoint(ResultPoint point) {

    }

    public interface ChangeListener {
        void start(boolean isExpand);

        void end(boolean isExpand);
    }
}
