package com.ybmmarket20.bean.payment;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

public class VirtualGoldRechargeTextBean implements Parcelable {

    private String text;
    private String color;

    protected VirtualGoldRechargeTextBean(Parcel in) {
        text = in.readString();
        color = in.readString();
    }

    public static final Creator<VirtualGoldRechargeTextBean> CREATOR = new Creator<VirtualGoldRechargeTextBean>() {
        @Override
        public VirtualGoldRechargeTextBean createFromParcel(Parcel in) {
            return new VirtualGoldRechargeTextBean(in);
        }

        @Override
        public VirtualGoldRechargeTextBean[] newArray(int size) {
            return new VirtualGoldRechargeTextBean[size];
        }
    };

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel dest, int flags) {
        dest.writeString(text);
        dest.writeString(color);
    }
}
