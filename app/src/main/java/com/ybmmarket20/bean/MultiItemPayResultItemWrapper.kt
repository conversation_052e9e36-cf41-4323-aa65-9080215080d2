package com.ybmmarket20.bean

import com.chad.library.adapter.base.entity.MultiItemEntity
import com.ybm.app.bean.AbstractMutiItemEntity

/**
 * <AUTHOR> Brin
 * @date : 2020/12/12 - 16:44
 * @Description :
 * @version
 */
class MultiItemPayResultItemWrapper(
        var tag: TagBean? = null,
        var coupon: VoucherListBean? = null
) : AbstractMutiItemEntity() {

    companion object {
        val TagBeanType = 1
        val CouponType = 2
        val UnreceivedCouponHeaderType = 3
        val ReceivedCouponHeaderType = 4
    }
}