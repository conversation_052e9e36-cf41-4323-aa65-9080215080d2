package com.ybmmarket20.bean;


import com.ybm.app.bean.AbstractMutiItemEntity;

public class TransactionDetailsChildrenBean extends AbstractMutiItemEntity {

    public static final int ITEMTYPE_HEAD = 1;
    public static final int ITEMTYPE_CONTENT = 2;
    private static final long serialVersionUID = -8223987313403852146L;

    private String date;//日期
    private double totalAmount;//合计金额
    private int size;//共几笔

    private String id;
    //用户编码
    private String merchantId;
    //金融产品编码
    private String financeCode;
    //订单编号
    private String orderNo;
    //订单编号
    private String orderId;
    //交易金额
    private double amount;
    //还款日
    private long repaymentDate;
    //创建时间
    private long createTime;
    //总期数
    private int instalmentTotal;
    //0未还清,1已还清
    private int fullRepayment;
    //0未逾期,1逾期
    private int overdue;
    //时间字符串
    private String createTimeStr;

    //交易是否成功文案
    private String transStatusName;

    //利息补贴金额
    private double actualInterest;

    private int parentPostPos;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getFinanceCode() {
        return financeCode;
    }

    public void setFinanceCode(String financeCode) {
        this.financeCode = financeCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public long getRepaymentDate() {
        return repaymentDate;
    }

    public void setRepaymentDate(long repaymentDate) {
        this.repaymentDate = repaymentDate;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getInstalmentTotal() {
        return instalmentTotal;
    }

    public void setInstalmentTotal(int instalmentTotal) {
        this.instalmentTotal = instalmentTotal;
    }

    public int getFullRepayment() {
        return fullRepayment;
    }

    public void setFullRepayment(int fullRepayment) {
        this.fullRepayment = fullRepayment;
    }

    public int getOverdue() {
        return overdue;
    }

    public void setOverdue(int overdue) {
        this.overdue = overdue;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public String getTransStatusName() {
        return transStatusName;
    }

    public void setTransStatusName(String transStatusName) {
        this.transStatusName = transStatusName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public int getParentPostPos() {
        return parentPostPos;
    }

    public void setParentPostPos(int parentPostPos) {
        this.parentPostPos = parentPostPos;
    }

    public double getActualInterest() {
        return actualInterest;
    }

    public void setActualInterest(double actualInterest) {
        this.actualInterest = actualInterest;
    }

}
