package com.ybmmarket20.bean;

import com.ybm.app.bean.AbstractMutiItemEntity;

import java.io.Serializable;
import java.util.List;

/**
 * author : 朱勇闯
 * e-mail : <EMAIL>
 * date   : 2024/10/25 14:25
 * desc   :
 */
public class RemindProgressBean implements Serializable {

    public static final int ITEMTYPE_TEXT = 1;
    public static final int ITEMTYPE_LIST = 2;
    private int expireTimeRemain;
    private int cancelExpedite;
    private List<ShippingReminderHistoryBean> shippingReminderHistory;

    public static class ShippingReminderHistoryBean extends AbstractMutiItemEntity implements Serializable {
        private String historyCreateTime;
        private String eventStatusStr;
        private CustomFieldsBean customFields;

        public ShippingReminderHistoryBean() {
            itemType = ITEMTYPE_TEXT;
        }

        @Override
        public int getItemType() {
            if (itemType <= 0) {
                return ITEMTYPE_TEXT;
            }
            return super.getItemType();
        }

        public static class CustomFieldsBean {
            private String prompt;
            private String appealCategory;
            private String appealDescription;
            private String appealEvidence;
        }
    }
}
