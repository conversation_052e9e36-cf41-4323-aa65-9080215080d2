package com.ybmmarket20.bean.cart

import com.chad.library.adapter.base.entity.MultiItemEntity

/**
 * getCartDataWork这个方法映射了一道
 * @property cartEntityList MutableList<MultiItemEntity>
 * @property totalAmount String?
 * @property discountsStr String?
 * @property commitText String?
 * @property isSelected Boolean
 * @property specialTipsShow Boolean
 * @property isLoading Boolean
 */
class CartBeanWraper : CartBean() {

    companion object {
        val CLOSE_AN_ACCOUNT = "去结算"
        val CLOSE_AN_ACCOUNT_COUPON = "领券结算"
        val DELETE = "删除"
        val EDIT = "编辑"
        val ACCOMPLISH = "完成"
    }

    var cartEntityList = mutableListOf<MultiItemEntity>()
    var totalAmount: String? = null
    var discountsStr: String? = null
    var commitText: String? = "去结算"
    var isSelected: Boolean = false
    var specialTipsShow :Boolean = false
    var isLoading = false

}


data class CartLayoutType(var styleTemplate: Int? = 2)