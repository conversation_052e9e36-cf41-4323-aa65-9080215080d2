package com.ybmmarket20.bean

import com.ybmmarket20.bean.loadmore.IPage

/**
 * <AUTHOR>
 * @date 2020-05-09
 * @description
 */
open class RefreshWrapperPagerBean<T>(
        val rows: MutableList<T>?,
        val licenseStatus: Int,
        val spId: String?,
        val sid: String?,
        val spType: String?,
        var nsid: String?,
        var listoffset: String?,
        var listdata: String?,
        var requestParam: Map<String, String>?,
        var isEnd: Boolean,
        var sptype: String?,
        var spid: String?,
): IPage<T> {
    var qtListData: String? = null
    var scmId: String? = null
    var expId: String? = null
    override fun getCurPage(): Int = 0

    override fun getPageRowSize(): Int = 0

    override fun getTotalPages(): Int = 0

    override fun getRowsList(): MutableList<T>? = rows
}

data class PagerBean (
    val rows: MutableList<RowsBean>,
    val licenseStatus: Int,
    val spId: String?,
    val sid: String?,
    val spType: String?,
    var nsid: String?,
    var listoffset: String?,
    var listdata: String?,
    var requestParam: Map<String, String>?,
    var isEnd: <PERSON>olean
)