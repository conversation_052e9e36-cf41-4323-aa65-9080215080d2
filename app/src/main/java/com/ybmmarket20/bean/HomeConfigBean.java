package com.ybmmarket20.bean;


import com.ybmmarket20.xyyreport.spm.TrackData;

/**
 * 首页动态配置
 */
public class HomeConfigBean {

    public String hot_search_key;//搜索关键词
    public String kefu_phone;//客服电话
    public String bottom_centre_button_img_url;//底部活动ab点击后的图片
    public String bottom_centre_button_select_url;//底部活动tab点击后的图片
    public String bottom_centre_button_action;//底部活动tab点击事件

    public String bottom_first_button_img_url;//首页tab1的图片  首页
    public String bottom_first_button_img_select_url;//首页tab的点击后的图片
    public String bottom_first_button_text;//首页tab的文案
    public TrackData bottom_first_button_track_data;

    public String bottom_second_button_img_url;//首页tab2的图片  常购清单、发现
    public String bottom_second_button_img_select_url;//首页tab的点击后的图片
    public String bottom_second_button_text;//首页tab的文案
    public String bottom_second_button_link;//首页tab
    public TrackData bottom_second_button_track_data;

    public String bottom_third_button_img_url;//首页tab3的图片   购物车
    public String bottom_third_button_img_select_url;//首页tab的点击后的图片
    public String bottom_third_button_text;//首页tab的文案
    public TrackData bottom_third_button_track_data;

    public String bottom_fourth_button_img_url;//首页tab4的图片   订单
    public String bottom_fourth_button_img_select_url;//首页tab的点击后的图片
    public String bottom_fourth_button_text;//首页tab的文案
    public TrackData bottom_fourth_button_track_data;

    public String bottom_frequently_button_img_url;//首页tab4的图片  常购常搜
    public String bottom_frequently_button_img_select_url;//首页tab的点击后的图片
    public String bottom_frequently_button_text;//首页tab的文案
    public TrackData bottom_frequently_button_track_data;

    // 发现页
    public String bottom_fifth_button_img_url;//首页tab5的图片   我的
    public String bottom_fifth_button_img_select_url;//首页tab的点击后的图片
    public String bottom_fifth_button_text;//首页tab的文案
    public TrackData bottom_fifth_button_track_data;

    public String top_search_left_ground;//搜索背景色
    public String top_search_right_ground;//搜索背景色
    public String bottom_background_image;//底部背景图片
    public String bottom_text_color;//底部字体色选中
    public String bottom_text_color_def;//底部字体色默认
    public int checkIcon;//搜索栏icon根据活动改变


    public boolean IsShowIcon() {
        return checkIcon == 1;
    }

    public boolean needUpdate(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        HomeConfigBean that = (HomeConfigBean) o;

        return bottom_centre_button_action != null ? bottom_centre_button_action.equals(that.bottom_centre_button_action) : that.bottom_centre_button_action == null;

    }

}
