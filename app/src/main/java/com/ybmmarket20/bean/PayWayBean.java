package com.ybmmarket20.bean;

import com.google.gson.annotations.SerializedName;

/**
 * 支付方式
 */
public class PayWayBean {
    //在线支付
    public static final int PAY_TYPE_ONLINE = 1;
    public String payway;
    public int payType; // 1:在线支付.6:他人支付.3:线下转账 12农行e贷
    public String tips;
    public String msg;
    public String color;
    public boolean checked;
    public String tipsDesc;
    public String unpcaTips;
    public String selectedTips;

    public int isSelected;
    @SerializedName("isOverdue")
    public int overdue; //1：逾期，0：没有逾期
    public PayConfigBean cashier;

    public PayTypeConfigV2Bean payTypeConfigV2Bean;

    public boolean isOverdue() {
        return overdue == 1;
    }
}
