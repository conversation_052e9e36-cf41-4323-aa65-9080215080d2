package com.ybmmarket20.bean;

import com.ybmmarketkotlin.bean.CommodityComboBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-02-11
 * @description
 */
public class ProductDetailBeanWrapper {
    public int licenseStatus;
    public ProductDetailBean rows;
    // 拼团
    public ActPtBean actPt;
    // 秒杀
    public SeckillBean actSk;
    // 批购包邮
    public ActPtBean actPgby;
    public int isAssemble;//是否拼团 0否 1是
    public int isWholesale;//是否是批购包邮 0否 1是

    //店铺相关
    public int isShowShop;//是否显示店铺
    public ShopInfo shopInfo;//店铺入口信息

    //备注: 配送说明 为NULL商详页不展示配送说明模块
    public DeliveryInfoBean deliveryInfo;

    //套餐相关
    public List<CommodityComboBean> productPrckagelList;//套餐一 套餐二 套餐三

    public ShopInfo getShopInfo() {
        return shopInfo;
    }

    public boolean getIsShowShop() {
        return isShowShop == 1;
    }

    public String getJgProductType(){
        String productType = "";
        if (getIsAssemble()){
            productType = "拼团";
        }else if (actSk != null){
            productType = "秒杀";
        }else if (getIsWholeSale()){
            productType = "批购包邮";
        }else {
            productType = "普通品";
        }
        return productType;
    }

    public double getJgProductPrice() {
        try {
            double jgProductPrice = 0.0;
            // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
            if (actPt != null && (actPt.assembleStatus == 1 || actPt.assembleStatus == 0)) {
                jgProductPrice = actPt.assemblePrice;
            } else if (actSk != null && actSk.status == 1) {
                jgProductPrice = actSk.skPrice;
            }else if(actPgby != null) {
                jgProductPrice = actPgby.assemblePrice;
            } else {
                if (rows!=null){
                    jgProductPrice = rows.fob;
                }
            }
            return jgProductPrice;
        }catch (Exception e){
            e.printStackTrace();
            return rows.fob;
        }
    }

    public double getShowPrice() {
        try {
            double jgProductPrice = 0.0;
            // 拼团中的商品，取拼团价/ 秒杀的商品取秒杀价
            if (actPt != null && (actPt.assembleStatus == 1 || actPt.assembleStatus == 0)) {
                jgProductPrice = actPt.assemblePrice;
            } else if (actSk != null && actSk.status == 1) {
                jgProductPrice = actSk.skPrice; // 秒杀详情用的fob 不知道为啥没有返actSk.skPrice
                if (jgProductPrice == 0.0){
                    jgProductPrice = rows.fob;
                }
            }else if(actPgby != null) {
                jgProductPrice = actPgby.assemblePrice;
            } else {
                if (rows!=null){
                    jgProductPrice = rows.fob;
                }
            }
            return jgProductPrice;
        }catch (Exception e){
            e.printStackTrace();
            return rows.fob;
        }
    }

    public ActPtBean getAct() {
        if (actPt == null && actPgby != null) {
            actPt = actPgby;
        }
        return actPt;
    }

    public boolean getIsAssemble() {
        return isAssemble == 1;
    }

    public boolean getIsWholeSale() {
        return isWholesale == 1;
    }

    public static class ShopInfo {

        public String shopLogoUrl;
        public String shopName;
        public String shopHomeUrl;
        public String shopUrl;
        public List<String> shopTag;
        public List<TagBean> shopPropTags;
        public String shelvesTag;
        public String salesVolumeTag;

    }
}
