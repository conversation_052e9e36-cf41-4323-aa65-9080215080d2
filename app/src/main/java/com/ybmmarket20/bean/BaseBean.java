package com.ybmmarket20.bean;

import android.text.TextUtils;


/**
 * 通用bean
 */
public class BaseBean<T> {

    public String status;
    public String msg;
    public int code;
    public boolean isFromCache;
    public CommonDialog dialog;
    public T data;

    public boolean isSuccess() {
        if (TextUtils.isEmpty(status)) {
            return false;
        }
        return status.toLowerCase().equals("success");
    }

    public T getData() {
        return data;
    }

    public BaseBean(BaseBean<?> baseBean, T t) {
        if (baseBean != null) {
            status = baseBean.status;
            msg = baseBean.msg;
            code = baseBean.code;
            isFromCache = baseBean.isFromCache;
            dialog = baseBean.dialog;
            data = t;
        }
    }

    public BaseBean() {
    }

    public static <E> BaseBean<E> newSuccessBaseBean(E data) {
        BaseBean<E> baseBean = new BaseBean<>();
        baseBean.status = "success";
        baseBean.data = data;
        return baseBean;
    }

    public static <E> BaseBean<E> newFailureBaseBean(E data) {
        BaseBean<E> baseBean = new BaseBean<>();
        baseBean.data = data;
        return baseBean;
    }
}
