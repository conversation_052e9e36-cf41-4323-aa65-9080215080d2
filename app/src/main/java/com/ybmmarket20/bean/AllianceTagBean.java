package com.ybmmarket20.bean;

import com.ybmmarket20.view.flowtag.OptionCheck;

/**
 * Author ： Love<PERSON><PERSON>weetheart
 * Date:2019/4/11
 */
public class AllianceTagBean implements OptionCheck {
    public AllianceTagBean(String typeName) {
        this.typeName = typeName;
    }

    private String id;
    private String typeName;
    private boolean isCheck = false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public boolean isChecked() {
        return isCheck;
    }

    @Override
    public void setChecked(boolean checked) {
        isCheck = checked;
    }

    @Override
    public boolean isMutual() {
        return false;
    }
}
