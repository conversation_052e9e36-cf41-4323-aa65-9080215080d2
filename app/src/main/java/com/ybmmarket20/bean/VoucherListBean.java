package com.ybmmarket20.bean;


import com.ybm.app.bean.AbstractMutiItemEntity;

import java.io.Serializable;
import java.util.List;

/**
 * 2016/7/6.
 * 优惠券
 */
public class VoucherListBean extends AbstractMutiItemEntity implements Serializable {

    private static final long serialVersionUID = -5173747428627465718L;

    public static final int COUPON_STATUS_UNRECEIVE = 1; //未领取
    public static final int COUPON_STATUS_RECEIVED = 2; //已领取
    public static final String QUERY_COUPON_TYPE_SHOP = "1";//查询店铺券
    public static final String QUERY_COUPON_TYPE_PLATFORM = "2";//查询大平台券

    public int id;//优惠券id
    public int voucherTemplateId;
    public int templateId;
    public String merchantId;  //商户id
    public int state;   //1，未领取   2，已领取  3，已用完  4，失效   5，已删除  6，抢光了


    //支付结果页专用
    public int activityState;   //1:即将开始 2:立即领取 3:立即使用 4:已使用 5:已抢光

    public long validDate; //有效时间
    public long expireDate; //失效时间
    public String appUrl; //使用优惠券跳转地址
    public String creator;
    public long createTime;
    public String updator;
    public long updateTime;
    public String remark;
    public String voucherTemplateName;
    public String merchantName;
    public String voucherDemo;
    public String voucherTitle;
    @Deprecated
    public boolean isChecked;           //选中状态
    public boolean isMutualExclusion;   //是否可以点击(同类型券互斥，只能选一个)
    public int manufacturerId;          //厂家id
    public int isUse;                   //是否是可用优惠券0，表示可使用，1表示不可以使用
    public double moneyInVoucher;       //金额
    public double sourceMoneyInVoucher; //每满减中，只满足一次的优惠券金额
    public String discount;              //折数
    public String discountDesc;         //折数文案
    public String minMoneyToEnableDesc; //订单满多少可用
    public String voucherDesc;
    public String djVoucherIds;//叠加券id集合
    public String voucherText;//不可选择文案
    /**
     * 通用券包括满减券-通用无门槛券-新人券
     */
    public int voucherType;             // 券的类型： 1-通用券，2-厂家券(即商品券)，3-折扣券，4-礼品券,5-新人券，  6-叠加券, 7-店铺券,8-平台券
    public String voucherTypeDesc;      // 券类型描述
    /**
     * 互斥关系
     * 1.通用券内部互斥，选择一张通用券，那么其他通用券不可使用
     * 2.通用券可以和厂家券一起使用
     * 3.不同厂家券可以一起使用
     * 4.厂家券内部互斥，也就是说一个厂家之中只能使用一张券
     */
    public int resultType;              // 用来做互斥的券类型   1，通用卷(其中通用券中还分新人券，使用VoucherType字段区分,可能是1,3,4,5 ) 2，厂家卷(即商品券)
    public int flag;//标识,是取时间段还是天数 0:时间段;1:天数
    public String validDays;//卷没用使用时间flag=1就取这个字段
    public int isSelectSkuNum;
    public double selectSkuAmount;
    public double noEnoughMoney;
    public String skuListTips = "";

    public boolean isSelected;
    public boolean canSelected;

    // 满减券新加字段
    public String voucherUsageWay;         //是 0满减 1每满减
    public String maxMoneyInVoucher;       //优惠券最高抵扣金额
    public String maxMoneyInVoucherDesc;   //优惠券最高抵扣金额

    /**
     * 1-打折券
     */
    public int voucherState;

    /**
     * 折扣比例  == discount
     */
    public String discountRatio;

    //店铺
    public String shopName;//店铺名
    public String voucherInstructions;//使用范围 全店使用
    public String voucherScope;//新的跟购物车保持一致 使用范围 全店使用
    public boolean choosen;//PC用的字段店铺券是否默认勾选（客户端需要遍历结算页settle接口里selectVoucherIds）
    public String title; //优惠提示
    public List<CartCouponGoods> cartProductList; //优惠券对应的商品列表
    public String expireDateToString;//过期时间
    public String validDateToString;//生效时间
    public String packageId; //套餐ID
    public String shopCode;//店铺编码
    public String shopPatternCode;//店铺业务模式编码：industry,工业; pop, 第三方; ybm,药帮忙; ykq,宜块钱; personal,自然人;
    public String canNotUseReason;//不可用优惠券不可用原因

    public int getVoucherTemplateId() {
        return voucherTemplateId;
    }

    public void setVoucherTemplateId(int voucherTemplateId) {
        this.voucherTemplateId = voucherTemplateId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getMoneyInVoucher() {
        return moneyInVoucher;
    }

    public void setMoneyInVoucher(double moneyInVoucher) {
        this.moneyInVoucher = moneyInVoucher;
    }

    public String getMinMoneyToEnableDesc() {
        return minMoneyToEnableDesc;
    }

    public void setMinMoneyToEnableDesc(String minMoneyToEnableDesc) {
        this.minMoneyToEnableDesc = minMoneyToEnableDesc;
    }

    public String getVoucherTypeDesc() {
        if (voucherTypeDesc == null) {
            voucherTypeDesc = "";
        }
        return voucherTypeDesc;
    }

    public void setVoucherTypeDesc(String voucherTypeDesc) {
        this.voucherTypeDesc = voucherTypeDesc;
    }

    public void setVoucherDesc(String voucherDesc) {
        this.voucherDesc = voucherDesc;
    }

    public void setVoucherDemo(String voucherDemo) {
        this.voucherDemo = voucherDemo;
    }

    public void setDjVoucherIds(String djVoucherIds) {
        this.djVoucherIds = djVoucherIds;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public void setMutualExclusion(boolean mutualExclusion) {
        isMutualExclusion = mutualExclusion;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        VoucherListBean that = (VoucherListBean) o;
        return id == that.id;
    }

    @Override
    public int hashCode() {
        return id;
    }

    public int getVoucherType() {
        return voucherType;
    }

    public void setVoucherType(int voucherType) {
        this.voucherType = voucherType;
    }

    public String getShopName() {
        if (shopName == null) {
            shopName = "";
        }
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getVoucherTitle() {
        return voucherTitle;
    }

    public void setVoucherTitle(String voucherTitle) {
        this.voucherTitle = voucherTitle;
    }

    public String getVoucherScope() {
        return voucherScope;
    }

    public void setVoucherScope(String voucherScope) {
        this.voucherScope = voucherScope;
    }
}
