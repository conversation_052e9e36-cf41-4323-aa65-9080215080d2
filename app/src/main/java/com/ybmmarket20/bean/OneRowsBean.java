package com.ybmmarket20.bean;


import com.ybm.app.bean.AbstractMutiItemEntity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品分类
 */
public class OneRowsBean extends AbstractMutiItemEntity implements Serializable {


    //一级分类
    public static final int FILTER_CATEGORY_LEVEL_FIRST = 1;
    //二级分类
    public static final int FILTER_CATEGORY_LEVEL_SECOND = 2;
    //三级分类
    public static final int FILTER_CATEGORY_LEVEL_THIRD = 3;

    public int id;
    public String code;
    public String name;
    public int priority;
    public int parentId;
    public long createTime;

    public String nickname;
    public int skuContainFragileNum;
    public int skuNotContainFragileNum;

    public int productNum;

    //父级
    public OneRowsBean parent;
    //id和bean的映射关系
    public Map<Integer, OneRowsBean> childrenMap;
    public List<OneRowsBean> children;

    public int level;

    public boolean isSelected = false;


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setProductNum(int productNum) {
        this.productNum = productNum;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public List<OneRowsBean> getRows() {
        return children;
    }

    public void setRows(List<OneRowsBean> rows) {
        this.children = rows;
    }

}
