package com.ybmmarket20.view;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.luck.picture.lib.tools.ScreenUtils;
import com.mcxtzhang.indexlib.suspension.SuspensionDecoration;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.ManufacturersBean;
import com.ybmmarket20.bean.SearchFilterBean;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 全部药品页-点击厂家按钮弹出pop
 */
@Deprecated
public class Manufacturers2Pop extends BaseFilterPopWindow {

    private List<ManufacturersBean> mDatas = new ArrayList<>();
    protected List<String> lastNames = new ArrayList<>();
    protected SuspensionDecoration mDecoration;
    protected YBMBaseAdapter adapter;

    private EditText mEtSearch;
    private ImageView mIvDel;
    private CommonRecyclerView mRvList;
    private IndexBar mIndexBar;
    private TextView mTvSideBarHint;
    private Button mBtnReset;
    private Button mBtnAffirm;

    private boolean isAvailable = false;
    private boolean isPromotion = false;
    private boolean isClassA = false;
    private boolean isClassB = false;
    private boolean isClassRx = false;
    private boolean isClassElse = false;
    private String priceRangeFloor = "";
    private String priceRangeTop = "";
    private String drugClassification = "";
    private String categoryId = "";
    private String commonName = "";
    private String shopCodes = null;
    private String mMasterStandardProductId = null;
    private String mManufacturersUrl = AppNetConfig.FIND_MANUFACTURER;

    private String mSearchScene = null;

    private RequestParams paramsV2;

    public Manufacturers2Pop(){}

    public Manufacturers2Pop(String masterStandardProductId) {
        mMasterStandardProductId = masterStandardProductId;
    }

    public Manufacturers2Pop(String masterStandardProductId, String url, RequestParams paramsV2) {
        mMasterStandardProductId = masterStandardProductId;
        mManufacturersUrl = url;
        this.paramsV2 = paramsV2;
    }

    public void setRequestParamsV2(RequestParams paramsV2) {
        this.paramsV2 = paramsV2;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.manufacturers2_pop;
    }

    @Override
    protected void initView() {

        mEtSearch = getView(R.id.et_search);
        mIvDel = getView(R.id.iv_del);
        mRvList = getView(R.id.rv_list);
        mIndexBar = getView(R.id.indexBar);
        mTvSideBarHint = getView(R.id.tvSideBarHint);
        mBtnReset = getView(R.id.btn_reset);
        mBtnAffirm = getView(R.id.btn_affirm);

        contentView.findViewById(R.id.bg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
//                setBtnAffirm();
            }
        });

        //重置
        mBtnReset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                reset(false);
            }
        });

        //确定
        mBtnAffirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setBtnAffirm();
            }
        });


        mIvDel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mEtSearch.setText("");
            }
        });

        mEtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s != null && s.length() > 0) {//输入文字
                    mIvDel.setVisibility(View.VISIBLE);
                    if (mRvList.isEnabled()) {//禁止下拉刷新
                        mRvList.setEnabled(false);
                    }
                    searchName(s.toString());
                } else {//清空
                    mIvDel.setVisibility(View.GONE);
                    setNewData(true, mDatas);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        mDecoration = new SuspensionDecoration(mRvList.getContext(), mDatas);
        mDecoration.setColorTitleBg(com.ybm.app.utils.UiUtils.getColor(R.color.choose_eara_item_press_color));
        mDecoration.setColorTitleFont(com.ybm.app.utils.UiUtils.getColor(R.color.text_9494A6));
        mRvList.addItemDecoration(mDecoration);
        mRvList.setShowAutoRefresh(false);
        /*mRvList.setListener(new CommonRecyclerView.Listener() {
            @Override
            public void onRefresh() {
                getData(false);
            }

            @Override
            public void onLoadMore() {

            }
        });*/

        adapter = createAdapter();
        mRvList.setAdapter(adapter);
        mRvList.setLoadMoreEnable(false);
        //indexbar初始化
        mIndexBar.setmPressedShowTextView(mTvSideBarHint)//设置HintTextView
                .setNeedRealIndex(true)//设置需要真实的索引
                .setDataHelper(new IndexBarDataHelper())
                .setSourceDatasAlreadySorted(false)
                .setmLayoutManager((WrapLinearLayoutManager) mRvList.getLayoutManager())
                .setIndexColor(R.color.base_colors_new).setPadding(ConvertUtils.dp2px(6))
                .setHeaderViewCount(1);//设置RecyclerView的LayoutManager
    }

    public YBMBaseAdapter createAdapter() {
        return new YBMBaseAdapter<ManufacturersBean>(R.layout.choose_item_shop, mDatas) {
            @Override
            protected void bindItemView(final YBMBaseHolder holder, final ManufacturersBean bean) {

                TextView tv = holder.getView(R.id.tv);
                tv.setActivated(lastNames.contains(bean.manufacturer));
                tv.setText(bean.manufacturer);
                tv.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {

                        boolean activated = view.isActivated();
                        if (activated) {
                            view.setActivated(false);
                            if (lastNames != null) {
                                lastNames.remove(bean.manufacturer);
                            }
                        } else {
                            view.setActivated(true);
                            if (lastNames != null) {
                                lastNames.add(bean.manufacturer);
                            }
                            notifyDataSetChanged();
                        }
                    }
                });
            }
        };
    }

    private void setBtnAffirm() {
        if (mOnSelectListener != null) {
            mOnSelectListener.getValue(new SearchFilterBean(lastNames));
        }
        dismiss();
    }

    private void searchName(String name) {
        if (mDatas == null || mDatas.isEmpty() || TextUtils.isEmpty(name)) {
            return;
        }

        name = name.trim();
        int size = mDatas.size();
        List<ManufacturersBean> temp = new ArrayList<>();
        ManufacturersBean bean = null;
        for (int a = 0; a < size; a++) {
            bean = mDatas.get(a);
            if (bean != null && bean.manufacturer != null && bean.manufacturer.contains(name)) {
                temp.add(bean);
            }
        }
        setNewData(false, temp);
    }

    private void setNewData(boolean isAll, List<ManufacturersBean> list) {
        if (isAll) {
            mRvList.getRecyclerView().removeItemDecoration(mDecoration);
            mRvList.addItemDecoration(mDecoration);
            if (!list.isEmpty()) {
                mIndexBar.setVisibility(View.VISIBLE);
            } else {
                mIndexBar.setVisibility(View.GONE);
            }
            mIndexBar.setmSourceDatas(list);//设置数据
            mDecoration.setmDatas(list);
            mIndexBar.requestLayout();
        } else {
            mRvList.getRecyclerView().removeItemDecoration(mDecoration);
            mIndexBar.setVisibility(View.GONE);
        }
        adapter.setNewData(list);
    }

    public void getData(ArrayList<ManufacturersBean> mList){
        mRvList.setRefreshing(true);
        setNewData(true, mList);
    }

    public void getData(boolean show) {
        if (show) {
            mRvList.setRefreshing(true);
        }
        RequestParams rp = null;
        if (paramsV2 != null) {
            rp = paramsV2;
            rp.setUrl(mManufacturersUrl);
        } else {
            rp = getParams();
        }
        if (mSearchScene != null) {
            rp.put("searchScene", mSearchScene);
        }
        HttpManager.getInstance().post(rp, new BaseResponse<List<ManufacturersBean>>() {

            @Override
            public void onSuccess(String content, BaseBean<List<ManufacturersBean>> obj, List<ManufacturersBean> manufacturersBeans) {

                mRvList.setRefreshing(false);
                mDatas.clear();
                if (manufacturersBeans != null && !manufacturersBeans.isEmpty()) {
                    mDatas.addAll(manufacturersBeans);
                    if (mOnLoadedDataCallback != null) {
                        mOnLoadedDataCallback.onLoadedData(mDatas);
                    }
                    setNewData(true, mDatas);
                }
            }

            @Override
            public void onFailure(NetError error) {
                mRvList.setRefreshing(false);
                super.onFailure(error);
            }
        });
    }

    public void setSearchScene(String searchScene) {
        mSearchScene = searchScene;
    }

    private OnLoadedDataCallback mOnLoadedDataCallback;

    public interface OnLoadedDataCallback {
        void onLoadedData(List<ManufacturersBean> mData);
    }

    public void setLoadedDataCallback(OnLoadedDataCallback onLoadedDataCallback) {
        mOnLoadedDataCallback = onLoadedDataCallback;
    }

    private RequestParams getParams() {
        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.setUrl(mManufacturersUrl);
        params.put("merchantId", merchantid);

        //全部分类 categoryId
        if (!TextUtils.isEmpty(categoryId)) {
            params.put("categoryIdsStr", categoryId);
        }
        //搜索关键字 showName
        if (!TextUtils.isEmpty(commonName)) {
            params.put("keyword", commonName);
        }
        if (!TextUtils.isEmpty(commonName)) {
            params.put("queryWord", commonName);
        }
        //经营类型
        drugClassification = setDrugsClass();
        if (!TextUtils.isEmpty(drugClassification)) {
            params.put("drugClassificationStr", drugClassification);
        }
        //仅看有货
        if (isAvailable) {
            params.put("hasStock", "1");
        }
        //有促销
        if (isPromotion) {
            params.put("isPromotion", "1");
        }
        //价格区间-最低价
        if (!TextUtils.isEmpty(priceRangeFloor)) {
            params.put("minPrice", priceRangeFloor);
        }
        //价格区间-最高价
        if (!TextUtils.isEmpty(priceRangeTop)) {
            params.put("maxPrice", priceRangeTop);
        }

        if (shopCodes != null) {
            params.put("shopCodes", shopCodes);
        }

        if (!TextUtils.isEmpty(mMasterStandardProductId)) {
            params.put("masterStandardProductId", mMasterStandardProductId);
        }

        return params;
    }

    public void setShopCodes(String shopCodes) {
        this.shopCodes = shopCodes;
    }

    public void setDataType(String categoryId, String commonName, boolean isAvailable, boolean isPromotion, boolean isClassA, boolean isClassB
            , boolean isClassRx, boolean isClassElse, String priceRangeFloor, String priceRangeTop, List<String> list) {

        this.categoryId = categoryId;
        this.commonName = commonName;
        this.isAvailable = isAvailable;
        this.isPromotion = isPromotion;
        this.isClassA = isClassA;
        this.isClassB = isClassB;
        this.isClassRx = isClassRx;
        this.isClassElse = isClassElse;
        this.priceRangeFloor = priceRangeFloor;
        this.priceRangeTop = priceRangeTop;
        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();
        this.lastNames.addAll(list);

        mDatas.clear();
        if (mEtSearch != null) {
            mEtSearch.setText("");
        }
        adapter.setNewData(mDatas);
    }

    public void reset(boolean isBrand) {

        if (lastNames == null) {
            lastNames = new ArrayList<>();
        }
        lastNames.clear();

//        categoryId = "";
//        commonName="";

        if (isBrand) {
            categoryId = "";
            priceRangeFloor = "";
            priceRangeTop = "";
            drugClassification = "";

            isAvailable = false;
            isPromotion = false;

            isClassA = false;
            isClassB = false;
            isClassRx = false;
            isClassElse = false;
        }

        adapter.notifyDataSetChanged();
    }

    public void setLastNames(List<String> selectedManufacturerList) {
        lastNames = selectedManufacturerList;
        adapter = createAdapter();
        mRvList.setAdapter(adapter);
    }

    public void show(View token,ArrayList<ManufacturersBean> mList) {

        getData(mList);
//        showAtLocation(token, 0, ScreenUtils.dip2px(token.getContext(), 110));
        super.show(token);
    }

    public void show(View token) {

        getData(true);
        showAtLocation(token, 0, ScreenUtils.dip2px(token.getContext(), 110));
//        super.show(token);
    }


    private String setDrugsClass() {

        StringBuilder sb = new StringBuilder();
        if (isClassA) {
            sb.append("1").append(",");
        }
        if (isClassB) {
            sb.append("2").append(",");
        }
        if (isClassRx) {
            sb.append("3").append(",");
        }
        if (isClassElse) {
            sb.append("4").append(",");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
}
