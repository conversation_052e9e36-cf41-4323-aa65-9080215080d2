package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.Px;
import androidx.core.content.ContextCompat;

import com.ybmmarket20.R;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.utils.UiUtils;

/**
 * 订单详情
 */

public class OrderDetailPop {

    private Context context;
    private View contentView;
    private PopupWindow popwindow;
    TextView tvTotalNum;
    TextView tvFreightNum;
    TextView tvOrderCouponNum;
    TextView tvOrderFlNum;
    TextView tvOrderLimitTimeDiscountNum;
    TextView tvOrderRebateNum;
    LinearLayout ll_order_freight;
    LinearLayout ll_order_coupon;
    LinearLayout ll_order_balance;
    ImageView ivFreightIcon;
    private TextView tvFreight;
    private View bottomView;

    private TextView mTvOrderBalanceNum;
    private TextView tvOrderOnePriceNum;
    private TextView tvOrderRedEnvelopeNum;
    private LinearLayout ll_order_pay_discount;
    private TextView tv_pay_discount_num;

    private PopupWindow.OnDismissListener mListener;

    public OrderDetailPop(Context context) {
        this.context = context;
        init(context);
    }

    public void init(Context context) {
        this.contentView = LayoutInflater.from(context).inflate(R.layout.order_detail_pop, null);
        contentView.findViewById(R.id.bg).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        bottomView = contentView.findViewById(R.id.header);
        bottomView.setOnClickListener(v -> dismiss());
        tvTotalNum = (TextView) contentView.findViewById(R.id.tv_total_num);
        tvFreightNum = (TextView) contentView.findViewById(R.id.tv_freight_num);
        tvOrderCouponNum = (TextView) contentView.findViewById(R.id.tv_order_coupon_num);
        tvOrderFlNum = (TextView) contentView.findViewById(R.id.tv_order_fl_num);
        tvOrderLimitTimeDiscountNum = (TextView) contentView.findViewById(R.id.tv_order_limit_time_Discount_num);
        tvOrderRebateNum = (TextView) contentView.findViewById(R.id.tv_order_rebate_num);
        mTvOrderBalanceNum = (TextView) contentView.findViewById(R.id.tv_order_balance_num);
        tvOrderOnePriceNum = (TextView) contentView.findViewById(R.id.tv_order_one_price_num);
        tvOrderRedEnvelopeNum = contentView.findViewById(R.id.tv_order_red_envelope_num);
        ll_order_freight = contentView.findViewById(R.id.ll_order_freight);
        ll_order_coupon = contentView.findViewById(R.id.ll_order_coupon);
        ll_order_balance = contentView.findViewById(R.id.ll_order_balance);
        tvFreight = contentView.findViewById(R.id.tv_freight);
        ll_order_pay_discount = contentView.findViewById(R.id.ll_order_pay_discount);
        tv_pay_discount_num = contentView.findViewById(R.id.tv_pay_discount_num);
    }

    /**
     * * 设置是否显示运费提示按钮
     *
     * @param isShow   true: 显示
     * @param shopCode  店铺code
     */
    public void setFreightTipBtnShow(boolean isShow, String shopCode) {
        if (isShow) {
            Drawable drawable = ContextCompat.getDrawable(context, R.drawable.icon_hint_image_cart);
            if (drawable == null) return;
            drawable.setBounds(0, 0, ConvertUtils.dp2px(11), ConvertUtils.dp2px(11));
            tvFreight.setCompoundDrawables(null, null, drawable, null);
            tvFreight.setOnClickListener(v -> new FreightTipDialog(context).showTip(shopCode));
        } else {
            tvFreight.setCompoundDrawables(null, null, null, null);
        }
    }

    public void initData(double price, double discount, double rebate, double freight, double coupon, double balanceAmount, double fixedPriceAmount, double redPacketAmount, String payDiscount,double limitTimeDiscount) {
        tvTotalNum.setText("¥" + String.valueOf(UiUtils.transform(price)));
        tvFreightNum.setText("¥" + String.valueOf(UiUtils.transform(freight)));
        //全场优惠券
        setDiscountInfo(R.id.ll_order_coupon, tvOrderCouponNum, coupon, "-¥");
        //限时加补
        setDiscountInfo(R.id.ll_order_limit_time_Discount, tvOrderLimitTimeDiscountNum,limitTimeDiscount, "-¥");
        //全场满减
        setDiscountInfo(R.id.ll_order_fl, tvOrderFlNum, discount, "-¥");
        //余额抵扣
        setDiscountInfo(R.id.ll_order_balance, mTvOrderBalanceNum, balanceAmount, "-¥");
        //一口价
        setDiscountInfo(R.id.ll_order_one_price, tvOrderOnePriceNum, fixedPriceAmount, "-¥");
        //红包
        setDiscountInfo(R.id.ll_order_red_envelope, tvOrderRedEnvelopeNum, redPacketAmount, "-¥");
        //支付优惠
        setPayDiscount(payDiscount, "-¥");
    }

    /**
     * 设置支付优惠
     */
    private void setPayDiscount(String payDiscount, String symbol) {
        if (!TextUtils.isEmpty(payDiscount)) {
            ll_order_pay_discount.setVisibility(View.VISIBLE);
            tv_pay_discount_num.setText(symbol + payDiscount);
        } else {
            ll_order_pay_discount.setVisibility(View.GONE);
        }
    }

    private void setDiscountInfo(int parentId, TextView tvValue, double value, String symbol) {
        if (value >= 0.0001) {
            contentView.findViewById(parentId).setVisibility(View.VISIBLE);
            tvValue.setText(symbol + String.valueOf(UiUtils.transform(value)));
        } else {
            contentView.findViewById(parentId).setVisibility(View.GONE);
        }
    }


    private void initPop(View token) {
        popwindow = new PopupWindow(this.contentView, LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.MATCH_PARENT, true);
        popwindow.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#00000000")));
        popwindow.setFocusable(true);
        popwindow.setOutsideTouchable(true);
        popwindow.setOnDismissListener(() -> {
            backgroundAlpha(1f);
            if (mListener != null) {
                mListener.onDismiss();
            }
        });
    }

    /**
     * 设置距离底部距离
     *
     * @param bottom
     */
    public void setMarginBottom(@Px int bottom) {
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) bottomView.getLayoutParams();
        lp.height = bottom;
        bottomView.setLayoutParams(lp);
    }

    public boolean isShow() {
        if (popwindow == null) {
            return false;
        }
        return popwindow.isShowing();
    }

    public void setOnDismissListener(PopupWindow.OnDismissListener listener) {
        mListener = listener;
    }

    public void show(View token) {
        if (popwindow == null) {
            initPop(token);
        }
        try {
            if (popwindow.isShowing()) {
                popwindow.dismiss();
            }
        } catch (Exception e) {
            return;
        }
        try {
            popwindow.showAtLocation(token, Gravity.START | Gravity.TOP, 0, 0);
            // 设置popWindow的显示和消失动画
//            popwindow.setAnimationStyle(R.style.mypopwindow_anim_style);
        } catch (Exception e) {
            return;
        }
        backgroundAlpha(0.3f);
        popwindow.update();
    }

    public void dismiss() {
        if (popwindow != null) {
            try {
                backgroundAlpha(1f);
                popwindow.dismiss();
            } catch (Exception e) {

            }
        }
    }

    /**
     * 设置添加屏幕的背景透明度
     *
     * @param bgAlpha
     */
    public void backgroundAlpha(float bgAlpha) {
//        WindowManager.LayoutParams lp = ((BaseActivity) context).getWindow().getAttributes();
//        lp.alpha = bgAlpha; //0.0-1.0
//        ((BaseActivity) context).getWindow().setAttributes(lp);
    }

    /**
     * ka用户隐藏运费，展示满减和满折优惠金额；
     * 在initData之后使用
     */
    public void hiddenCountentForKA(boolean isHidden) {
        if (isHidden) {
//            if (ll_order_freight!=null){//11.11 麻黄碱  订单详情：展示运费；
//                ll_order_freight.setVisibility(View.GONE);
//            }
            if (ll_order_coupon != null) {
                ll_order_coupon.setVisibility(View.GONE);
            }
            if (ll_order_balance != null) {
                ll_order_balance.setVisibility(View.GONE);
            }
        } else {
//            if (ll_order_freight!=null){
//                ll_order_freight.setVisibility(View.VISIBLE);
//            }
            if (ll_order_coupon != null) {
                ll_order_coupon.setVisibility(View.VISIBLE);
            }
            if (ll_order_balance != null) {
                ll_order_balance.setVisibility(View.VISIBLE);
            }
        }
    }
}
