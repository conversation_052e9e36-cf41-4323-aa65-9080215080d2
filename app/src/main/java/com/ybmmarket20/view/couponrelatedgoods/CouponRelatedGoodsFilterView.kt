package com.ybmmarket20.view.couponrelatedgoods

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.activity.ComponentActivity
import com.ybmmarket20.R
import com.ybmmarket20.view.couponrelatedgoods.ComprehensiveSortPopupWindow.ComprehensiveItem

/**
 * 凑单页筛选栏 - 筛选框
 */

class CouponRelatedGoodsFilterView(context: Context, attr: AttributeSet?) :
    BaseCouponRelatedGoodsFilterView(context, attr) {

    var mResultCallback: ((params: Map<String, String>)->Unit)? = null

    //综合排序
    private var mComprehensiveSortPopupWindow: ComprehensiveSortPopupWindow? = null
    //全部分类
    private var mWholeCategoryPopupWindow: WholeCategoryPopupWindow? = null
    //商家
    private var mCouponShopStorePopupWindow: CouponShopStorePopupWindow? = null
    //筛选
    private var mCouponFilterClassify: CouponFilterClassify? = null

    /**
     * 设置数据
     */
    override fun setItemList(itemList: List<FilterItem>?, voucherId: String?, isDesignateShop: Boolean) {
        super.setItemList(itemList, voucherId, isDesignateShop)
        if (mCouponShopStorePopupWindow == null && context is ComponentActivity && isDesignateShop) {
            //点击前初始化：页面加载时需要反聚商家数据
            mCouponShopStorePopupWindow = CouponShopStorePopupWindow(context as ComponentActivity, voucherId) {isContainsSelected, _ ->
                if (isContainsSelected) {
                    val shopView = itemList?.find { it.id == FILTER_ITEM_TYPE_BUSINESS }?.view
                    shopView?.let { setPopupWindowSelectedStatus(true, it) }
                }
            }
        }
        if (mComprehensiveSortPopupWindow == null) {
            //点击前初始化：默认选项是销量，所以默认需要设置选中状态
            mComprehensiveSortPopupWindow = ComprehensiveSortPopupWindow()
            mComprehensiveSortPopupWindow!!.setData(mComprehensiveSortPopupWindow!!.getDataList1())
            val comprehensiveView = itemList?.find { it.id == FILTER_ITEM_TYPE_SORT }?.view
            comprehensiveView?.let { setPopupWindowSelectedStatus(true, it) }
        }
    }

    fun setOnResultCallback(resultCallback: ((params: Map<String, String>)->Unit)?) {
        mResultCallback = resultCallback
    }

    /**
     * 设置筛选popupwindow选择后按钮的状态
     */
    fun setPopupWindowSelectedStatus(containsSelectedResult: Boolean, v: View) {
        if (!containsSelectedResult) {
            setItemStatus(FILTER_ITEM_UNSELECTED, v, null)
        } else {
            setChildSelected(v)
        }
    }

    /**
     * 点击筛选
     */
    override fun handleFilterItems(filterItem: FilterItem, v: View) {
        when(filterItem.id) {
            //综合排序
            FILTER_ITEM_TYPE_SORT -> selectComprehensive(v)
            //分类
            FILTER_ITEM_TYPE_CATEGORY -> selectWholeCategories(v)
            //商家
            FILTER_ITEM_TYPE_BUSINESS -> selectShops(v)
            //筛选
            FILTER_ITEM_TYPE_FILTER -> selectFromFilter(v)
        }
    }

    /**
     * 选择综合排序
     */
    private fun selectComprehensive(v: View) {
        mComprehensiveSortPopupWindow!!.apply {
            setOnItemClickListener(object: ComprehensiveSortPopupWindow.IComprehensiveSortListener{
                override fun onItemClick(item: ComprehensiveItem?) {
                    setPopupWindowSelectedStatus(item != null, v)
                    val tvFilter = v.findViewById<TextView>(R.id.tvFilter)
                    tvFilter.text = item?.textResult
                    item?.let { mResultCallback?.invoke(mapOf("sortStrategy" to it.id)) }
                }

                override fun onCancel(item: ComprehensiveItem?) {
                    setPopupWindowSelectedStatus(item != null, v)
                }
            })
            show(this@CouponRelatedGoodsFilterView)
        }
    }

    /**
     * 选择全部分类
     */
    private fun selectWholeCategories(v: View) {
        if (mWholeCategoryPopupWindow == null) mWholeCategoryPopupWindow = WholeCategoryPopupWindow()
        mWholeCategoryPopupWindow?.apply {
            setOnResultListener(object: WholeCategoryPopupWindow.OnResultListener{
                override fun onConfirm(categoryIds: String?) {
                    setPopupWindowSelectedStatus(!categoryIds.isNullOrEmpty(), v)
                    mResultCallback?.invoke(mapOf("categoryIdsStr" to (categoryIds?: "")))
                }

                override fun onDismiss(categoryIds: String?) {
                    setPopupWindowSelectedStatus(!categoryIds.isNullOrEmpty(), v)
                }
            })
            showWithHandleIds(this@CouponRelatedGoodsFilterView)
        }
    }

    /**
     * 选择商家
     */
    private fun selectShops(v: View) {
        //mCouponShopStorePopupWindow已经提起初始化
        mCouponShopStorePopupWindow?.apply {
            setTypeVisible(false)
            setOnResultListener(object: CouponShopStorePopupWindow.OnResultListener{
                override fun onConfirm(shopCodes: String?) {
                    setPopupWindowSelectedStatus(!shopCodes.isNullOrEmpty(), v)
                    mResultCallback?.invoke(mapOf("shopCodes" to (shopCodes?: "")))
                }

                override fun onDismiss(shopCodes: String?) {
                    setPopupWindowSelectedStatus(!shopCodes.isNullOrEmpty(), v)
                }
            })
            show(this@CouponRelatedGoodsFilterView)
        }
    }

    /**
     * 从筛选框筛选
     */
    private fun selectFromFilter(v: View) {
        if (mCouponFilterClassify == null) {
            mCouponFilterClassify = CouponFilterClassify()
            mCouponFilterClassify?.init()
        }
        mCouponFilterClassify?.apply {
            setOnSelectListener(object: CouponFilterClassify.ISelectListener{
                override fun onDismiss(params: Map<String, Boolean>) {
                    setPopupWindowSelectedStatus(params.isNotEmpty(), v)
                }

                override fun onResult(params: Map<String, String>) {
                    mResultCallback?.invoke(params)
                }
            })
            show()
        }
    }
}