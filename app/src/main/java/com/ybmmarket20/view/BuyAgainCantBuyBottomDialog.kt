package com.ybmmarket20.view

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.FindSameGoodsAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FindSameGoodsResultBean
import com.ybmmarket20.bean.OrderBuyAgainProduct
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.dp
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.ImageUtil.Companion.load


/**
 * @class   BugAgainCantBuyBottomDialog
 * <AUTHOR>
 * @date  2025/1/21
 * @description 再次购买-商品无法购买推荐相似弹窗
 */
class BuyAgainCantBuyBottomDialog(val mContext: Context, val product: OrderBuyAgainProduct?) : Dialog(mContext) {

    private var recommendList: MutableList<RowsBean> = ArrayList()
    private var recommendAdapter: FindSameGoodsAdapter? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(getLayoutResID())
        val layoutParams = window?.attributes
        layoutParams?.gravity = Gravity.BOTTOM // 底部显示
        layoutParams?.width = ConstraintLayout.LayoutParams.MATCH_PARENT
        layoutParams?.height = 690.dp
        window?.attributes = layoutParams
        setCancelable(true)
        initUI()
        requestSameProduct()
    }

    private fun initUI() {
        val recyclerView = findViewById<RecyclerView>(R.id.rv_same_goods)
        recommendAdapter = FindSameGoodsAdapter(recommendList, mContext)
        recyclerView.adapter =  recommendAdapter
        recyclerView.layoutManager = StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL)
    }

    private fun requestSameProduct() {
        val params = RequestParams()
        params.put("pageSize","40")
        params.put("pageNum","1")
        params.put("skuId",product?.skuId?:"")

        HttpManager.getInstance().post(AppNetConfig.ORDER_FIND_SAME_PRODUCT, params, object : BaseResponse<FindSameGoodsResultBean?>() {

            override fun onSuccess(content: String?, obj: BaseBean<FindSameGoodsResultBean?>?, data: FindSameGoodsResultBean?) {
                if (obj?.isSuccess == true){
                    if (data!= null && !data.rows.isNullOrEmpty()){
                        val sameProduct = data.rows.removeAt(0)
                        setHeaderUI(sameProduct)
                        recommendList.addAll(data.rows)
                        recommendAdapter?.notifyDataSetChanged()
                    }
                }
            }
        })
    }

    private fun getLayoutResID():Int = R.layout.dialog_bug_again_cant_buy_bottom

    private fun setHeaderUI(rowsBean: RowsBean){
        val imageView = findViewById<ImageView>(R.id.iv_goods)
        val tvGoodsTitle = findViewById<TextView>(R.id.tv_goods_title)
        val tvCompany = findViewById<TextView>(R.id.tv_company)
        val tvPrice = findViewById<TextView>(R.id.tv_price)
        load(mContext, AppNetConfig.LORD_IMAGE + rowsBean.imageUrl, imageView)
        tvGoodsTitle.text = rowsBean.productName?:""
        tvCompany.text = rowsBean.manufacturer?:""
        tvPrice.text = rowsBean.showPriceStr
    }

}