package com.ybmmarket20.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.GestureDetector.SimpleOnGestureListener;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;

import com.bumptech.glide.load.resource.bitmap.GlideBitmapDrawable;

/**
 * 支持缩放 放大的ImageView
 */
public class ZoomImageView extends androidx.appcompat.widget.AppCompatImageView {
    /**
     * 模式 NONE：无 DRAG：拖拽. ZOOM:缩放
     */
    private enum MODE {
        NONE, DRAG, ZOOM
    }

    private MODE mode = MODE.NONE;// 默认模式
    /**
     * 用于记录图片要进行拖拉时候的坐标位置
     */
    private Matrix currentMatrix = new Matrix();
    private Matrix initMatrix = new Matrix();
    /**
     * 用于记录拖拉图片移动的坐标位置
     */
    private Matrix matrix = new Matrix();
    /**
     * 两个手指的开始距离
     */
    private float startDis;
    /**
     * 用于记录开始时候的坐标位置
     */
    private PointF lastPoint = new PointF();
    /**
     * 两个手指的中间点
     */
    private PointF midPoint;
    private Bitmap bitmap;
    private float scale;
    private GestureDetector mGestureDetector;
    private Boolean touchEventResult;
    private ScaleGestureDetector mScaleDragDetector;
    private RectF displayRect;// 显示区域相对于View区域的位置
    private final RectF mDisplayRect = new RectF();// 显示区域相对于View区域的位置
    private int mScrollEdge = EDGE_BOTH;
    static final int EDGE_NONE = -1;
    static final int EDGE_LEFT = 0;
    static final int EDGE_RIGHT = 1;
    static final int EDGE_BOTH = 2;
    private float totalTranslateX;
    private float totalTranslateY;
    private OnClickListener listener;
    private OnLongClickListener longClickListener;

    public ZoomImageView(Context context) {
        super(context);
        init(context);
    }

    public ZoomImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        setScaleType(ScaleType.MATRIX);
        scale = 1;
        mGestureDetector = new GestureDetector(context, gestureListener);
        mScaleDragDetector = new MyScaleGestureDetector(context, null);
    }

    /**
     * 还原为最初的大小
     */
    public void resetView() {
        setImageMatrix(initMatrix);
        scale = 1;
    }

    public void setOnClickListener(OnClickListener l) {
        this.listener = l;
    }

    public void setOnLongClickListener(OnLongClickListener longClickListener) {
        this.longClickListener = longClickListener;
    }

    SimpleOnGestureListener gestureListener = new SimpleOnGestureListener() {
        public boolean onDown(MotionEvent e) {
            return true;
        }

        public boolean onSingleTapConfirmed(MotionEvent e) {
            if (listener != null) {
                listener.onClick(ZoomImageView.this);
            }
            return true;
        }

        @Override
        public void onLongPress(MotionEvent e) {
            super.onLongPress(e);
            if (longClickListener != null) {
                longClickListener.onLongClick(ZoomImageView.this);
            }
        }
    };

    @Override
    public boolean onTouchEvent(MotionEvent event) {

        // Check to see if the user ScaleDrag
        if (null != mScaleDragDetector
                && mScaleDragDetector.onTouchEvent(event)) {
            touchEventResult = true;
        }

        // Check to see if the user double tapped
        if (null != mGestureDetector && mGestureDetector.onTouchEvent(event)) {
            touchEventResult = true;
        }

        return touchEventResult;
    }

    /**
     * 手指抬起的操作
     */
    private void pointerUp() {
        if (scale < 1) {
            setImageMatrix(initMatrix);
        }
    }

    /**
     * 计算两个手指间的距离
     */
    private float getDistance(MotionEvent event) {
        float distance = 0;
        try {
            float dx = event.getX(1) - event.getX(0);
            float dy = event.getY(1) - event.getY(0);
            distance = (float) Math.sqrt(dx * dx + dy * dy);
        } catch (IllegalArgumentException e) {
            // e.printStackTrace();
        }
        /** 使用勾股定理返回两点之间的距离 */
        return distance;
    }

    /**
     * 计算两个手指间的中间点
     */
    private PointF mid(MotionEvent event) {
        float midX = (event.getX(1) + event.getX(0)) / 2;
        float midY = (event.getY(1) + event.getY(0)) / 2;
        return new PointF(midX, midY);
    }

    /**
     * 检查是否超出边界
     */
    private void checkMatrixBounds() {
        displayRect = getDisplayRect();
        if (displayRect == null) {
            return;
        }
        if (displayRect.left > 0) {
            mScrollEdge = EDGE_LEFT;
        } else if (displayRect.right < getWidth()) {
            mScrollEdge = EDGE_RIGHT;
        } else {
            mScrollEdge = EDGE_NONE;
        }

        scale = (displayRect.right - displayRect.left) / getWidth();
    }

    /**
     * Helper method that maps the supplied Matrix to the current Drawable
     * - Matrix to map Drawable against
     *
     * @return RectF - Displayed Rectangle
     */

    private RectF getDisplayRect() {

        Drawable d = getDrawable();
        if (null != d) {
            mDisplayRect.set(0, 0, d.getIntrinsicWidth(),
                    d.getIntrinsicHeight());
            matrix.mapRect(mDisplayRect);
            return mDisplayRect;
        }
        return null;
    }

    /**
     * 将图片居中显示
     */
    @Override
    protected void onLayout(boolean changed, int left, int top, int right,
                            int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (getDrawable() != null) {
            if (getDrawable() instanceof GlideBitmapDrawable) {
                this.bitmap = ((GlideBitmapDrawable) getDrawable()).getBitmap();
            } else {
                this.bitmap = ((BitmapDrawable) getDrawable()).getBitmap();
            }
            getProperBaseMatrix(bitmap, initMatrix);
            setImageMatrix(initMatrix);
        }
    }

    /**
     * 图片居中显示
     *
     * @param bitmap
     * @param matrix
     */
    private void getProperBaseMatrix(Bitmap bitmap, Matrix matrix) {
        float viewWidth = getWidth();
        float viewHeight = getHeight();

        float w = bitmap.getWidth();
        float h = bitmap.getHeight();
        matrix.reset();

        // We limit up-scaling to 2x otherwise the result may look bad if it's
        // a small icon.
        float widthScale = viewWidth / w;
        float heightScale = viewHeight / h;
        float scale = Math.min(widthScale, heightScale);
        matrix.postScale(scale, scale);

        matrix.postTranslate((viewWidth - w * scale) / 2F, (viewHeight - h
                * scale) / 2F);
    }

    /**
     * 扩展缩放类 控制手指移动
     *
     * <AUTHOR>
     */
    class MyScaleGestureDetector extends ScaleGestureDetector {

        public MyScaleGestureDetector(Context context,
                                      OnScaleGestureListener listener) {
            super(context, listener);
        }

        @Override
        public boolean onTouchEvent(MotionEvent event) {

            if (bitmap == null) {
                return false;
            }

            /** 通过与运算保留最后八位 MotionEvent.ACTION_MASK = 255 */
            switch (event.getAction() & MotionEvent.ACTION_MASK) {
                // 手指压下屏幕
                case MotionEvent.ACTION_DOWN:
                    mode = MODE.DRAG;
                    // 记录ImageView当前的移动位置
                    currentMatrix.set(getImageMatrix());
                    totalTranslateX = 0;
                    totalTranslateY = 0;
                    lastPoint.set(event.getX(), event.getY());
                    if (scale > 1) {
                        getParent().requestDisallowInterceptTouchEvent(true);
                    }
                    break;
                // 手指在屏幕上移动，改事件会被不断触发
                case MotionEvent.ACTION_MOVE:

                    checkMatrixBounds();

                    // 拖拉图片
                    if (mode == MODE.DRAG) {
                        if (scale <= 1) {
                            break;
                        }
                        float dx = event.getX() - lastPoint.x; // 得到x轴的移动距离
                        float dy = event.getY() - lastPoint.y; // 得到x轴的移动距离
                        lastPoint.set(event.getX(), event.getY());

                        if ((scale <= 1) || (mScrollEdge == EDGE_LEFT && dx >= 1f)
                                || (mScrollEdge == EDGE_RIGHT && dx <= -1f)) {
                            getParent().requestDisallowInterceptTouchEvent(false);
                        } else {
                            getParent().requestDisallowInterceptTouchEvent(true);
                        }
                    /* 防止上下拖动 到达上下边界 */
                        if ((displayRect.top > 0 && dy > 0)
                                || (displayRect.bottom < getHeight() && dy < 0)) {
                            dy = 0;
                        }
                    /* 防止左右拖动 到达左右边界 */
                        if ((displayRect.left > 0 && dx > 0)
                                || (displayRect.right < getWidth() && dx < 0)) {
                            dx = 0;
                        }
                        totalTranslateX += dx;
                        totalTranslateY += dy;

                        // 在没有移动之前的位置上进行移动
                        matrix.set(currentMatrix);
                        matrix.postTranslate(totalTranslateX, totalTranslateY);
                        setImageMatrix(matrix);
                    }
                    // 放大缩小图片
                    else if (mode == MODE.ZOOM) {
                        float endDis = getDistance(event);// 结束距离
                        // 两个手指并拢在一起的时候像素大于10,
                        // 2018-1-9：midPoint在第二个手指按下与第一个手指距离小于10为null，move判断10和midPoint没关系
                        if (endDis > 10f && midPoint != null) {
                            matrix.set(currentMatrix);
                            matrix.postScale((endDis / startDis),
                                    (endDis / startDis), midPoint.x, midPoint.y);
                            setImageMatrix(matrix);
                        }
                    }
                    break;
                // 手指离开屏幕
                case MotionEvent.ACTION_UP:
                    // 当触点离开屏幕，但是屏幕上还有触点(手指)
                case MotionEvent.ACTION_POINTER_UP:
                    mode = MODE.NONE;
                    pointerUp();
                    break;
                // 当屏幕上已经有触点(手指)，再有一个触点压下屏幕
                case MotionEvent.ACTION_POINTER_DOWN:
                    mode = MODE.ZOOM;
                    /** 计算两个手指间的距离 */
                    startDis = getDistance(event);
                    /** 计算两个手指间的中间点 */
                    if (startDis > 10f) { // 两个手指并拢在一起的时候像素大于10
                        midPoint = mid(event);
                        // 记录当前ImageView的缩放倍数
                        currentMatrix.set(getImageMatrix());
                    }
                    break;
            }
            return true;
        }
    }

}
