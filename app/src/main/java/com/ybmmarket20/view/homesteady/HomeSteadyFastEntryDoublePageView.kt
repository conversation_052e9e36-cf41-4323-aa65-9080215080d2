package com.ybmmarket20.view.homesteady

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import com.luck.picture.lib.tools.ScreenUtils
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.FastEntry
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.common.JgTrackBean
import kotlin.math.ceil


/**
 * 快捷入口（两页）
 */
//第一页View
const val FASTENTRY_TAG_FIRST: Int = 0
//第二页View
const val FASTENTRY_TAG_SECOND: Int = 1

class HomeSteadyFastEntryDoublePageView(context: Context, attr: AttributeSet): BaseHomeSteadyView(context, attr) {

    val views = mutableListOf<View>()
    val vp: ViewPager by lazy {
        findViewById(R.id.vp_double_page)
    }
    val indicatorLeft: View by lazy {
        findViewById(R.id.view_double_indicator_left)
    }
    val indicatorRight: View by lazy {
        findViewById(R.id.view_double_indicator_right)
    }
    private val clDoubleIndicator: View by lazy {
        findViewById(R.id.cl_double_indicator)
    }
    private val firstChildView = HomeSteadyFastEntryView(context, true)
    private val secondChildView = HomeSteadyFastEntryView(context, true)
    private var mCallback: ((String, Int, FastEntryItem, View, Int, String) -> Unit)? = null
    var mSuperData: FastEntry? = null
    var jgTrackBean: JgTrackBean? = null
    var mHomeJgspid: String? = null
    override fun getLayoutId(): Int = R.layout.layout_home_steady_fast_entry_double_page

    override fun initPlaceHold() {
        super.initPlaceHold()
        firstChildView.initSingleLineHoldPlace()
        secondChildView.initSingleLineHoldPlace()
        views.add(firstChildView)
        views.add(secondChildView)
        initPagerAdapter(views)
    }

    val recordSubList = mutableListOf<MutableList<FastEntryItem>>()

    fun setFastEntryDataAll(data: MutableList<FastEntryItem>, entryRowNum: Int = 1) {
        if (data.isEmpty() || entryRowNum == 0) {
            visibility = View.GONE
            return
        }
        views.clear()
        val pageItemCount = 5 * entryRowNum
        val pageCount = ceil(data.size / (pageItemCount * 1.0)).toInt()
        var index = 0
        while (index != pageCount) {
            val subList = if (pageItemCount * (index + 1) >= data.size) {
                data.subList(pageItemCount * index, data.size).apply {
                    forEach {
                        it.pageIndex = index
                        it.pages = pageCount
                        it.pageItemCount = pageItemCount
                    }
                }
            } else data.subList(pageItemCount * index, pageItemCount * (index + 1)).apply {
                forEach {
                    it.pageIndex = index
                    it.pages = pageCount
                    it.pageItemCount = pageItemCount
                }
            }
            val subView = HomeSteadyFastEntryView(context, true)
            subView.tag = index
            subView.setAnalysisCallback(mCallback)
            subView.jgTrackBean = this.jgTrackBean
            subView.mSuperData = mSuperData
            subView.mHomeJgspid = this.mHomeJgspid
            // TODO: 李江 等11.9.8版本优化 再放开
//            subView.dataChangeCallBack = {
//                subView.post {
//                    synchronized(vp){
//                        val lp = vp.layoutParams as LayoutParams
//                        val mHeight = max(vp.height,subView.height)
////                lp.height = entryRowNum * ScreenUtils.dip2px(context, 74f)
//                        LogUtil.d("lj","height: $mHeight")
//                        lp.height = mHeight
//                        vp.layoutParams = lp
//                    }
//                }
//            }
            recordSubList.add(subList)

            views.add(subView)

            index++
        }
        try {
            val subViewFirst = views[0]
            val subDataFirst = recordSubList[0]
            (subViewFirst as HomeSteadyFastEntryView).setFastEntryData(subDataFirst)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        initPagerAdapter(views)
        val lp = vp.layoutParams as LayoutParams
        lp.height = entryRowNum * ScreenUtils.dip2px(context, 74f)
        vp.layoutParams = lp
        clDoubleIndicator.visibility = if (pageCount == 1) View.GONE else View.VISIBLE
    }

    private fun initPagerAdapter(views: MutableList<View>) {
        vp.adapter = DoublePageAdapter(views)
        vp.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
            }

            override fun onPageSelected(position: Int) {
                indicatorLeft.visibility = if (position == 0) View.VISIBLE else View.GONE
                indicatorRight.visibility = if (position == 1) View.VISIBLE else View.GONE
                try {
                    (views[position] as HomeSteadyFastEntryView).setFastEntryData(recordSubList[position])
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onPageScrollStateChanged(state: Int) {
            }

        })
    }

    inner class DoublePageAdapter(val views: MutableList<View>): PagerAdapter() {

        override fun getCount(): Int = views.size

        override fun isViewFromObject(view: View, obj: Any): Boolean = view == obj

        override fun destroyItem(container: ViewGroup, position: Int, `object`: Any) {
            if (position < views.size) {
                container.removeView(views[position])
            }
        }

        override fun instantiateItem(container: ViewGroup, position: Int): Any {
            container.addView(views[position])
            return views[position]
        }

    }

    /**
     * 设置埋点回调
     */
    fun setAnalysisCallback(callback: ((String, Int, FastEntryItem, View, Int, String) -> Unit)?) {
        mCallback = callback
    }

}