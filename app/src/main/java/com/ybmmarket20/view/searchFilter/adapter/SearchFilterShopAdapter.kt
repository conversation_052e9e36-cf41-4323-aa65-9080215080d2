package com.ybmmarket20.view.searchFilter.adapter

import android.widget.CheckBox
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.bean.searchfilter.SearchFilterContentBean

/**
 * 搜索过滤条件，商家Adapter
 */
class SearchFilterShopAdapter(list: MutableList<FullSearchFilterBean> = mutableListOf()) : AbsSearchFilterAdapter<FullSearchFilterBean>(list, R.layout.item_search_filter_manufacturer) {
    override fun getItemSpanSize(): Int = SEARCH_FILTER_SPAN_SIZE_SHOP

    override fun getAdapterType(): Int = SEARCH_FILTER_DATA_TYPE_SHOP

    override fun bindItemViewData(holder: Y<PERSON><PERSON><PERSON><PERSON>older, fullSearchFilterBean: FullSearchFilterBean) {
        if (fullSearchFilterBean is SearchFilterContentBean<*> && fullSearchFilterBean.contentBean is SearchFilterBean) {
            val bean = fullSearchFilterBean.contentBean as SearchFilterBean
            val cbItem = holder.getView<CheckBox>(R.id.cb_item)
            cbItem.text = bean.showName
            cbItem.isChecked = bean.isSelected
            holder.itemView.setOnClickListener {
                bean.isSelected = !bean.isSelected
                notifyDataSetChanged()
            }
        }
    }
}