package com.ybmmarket20.view;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybmmarket20.R;

public class ShowBottomCommonDialog extends BaseShowBottomSheetDialog {
    private TextView tvTop;
    private TextView tvBottom;
    private TextView tvCancel;

    public interface OnItemChooseListener{
        void OnTopClick();
        void OnBottomClick();
        void OnCancelClick();
    }

    public ShowBottomCommonDialog(Context context) {
        super(context);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.dialog_layout_common_bottom;
    }

    @Override
    protected void initView() {
        tvTop = getView(R.id.tv_top);
        tvBottom = getView(R.id.tv_bottom);
        tvCancel = getView(R.id.tv_cancel);

    }
    public void setItemText(String top,String bottom){
        if (tvTop!=null){

            tvTop.setText(top);
        }
        if (tvBottom!=null){

            tvBottom.setText(bottom);
        }
    }

    public void hiddenBottom() {
        if (tvBottom != null) {
            tvBottom.setVisibility(View.GONE);
        }
    }

    public void setOnItemChooseListener(OnItemChooseListener listener) {

        if (tvTop != null) {
            tvTop.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.OnTopClick();
                }
            });
        }
        if (tvBottom != null) {
            tvBottom.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.OnBottomClick();
                }
            });
        }
        if (tvCancel!=null){
            tvCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.OnCancelClick();
                }
            });
    }



    }
    @Override
    protected int getListH() {
        return 0;
    }
    @Override
    protected LinearLayout.LayoutParams getLayoutParams() {
        return new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }
}
