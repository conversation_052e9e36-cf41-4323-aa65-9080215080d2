package com.ybmmarket20.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import com.ybmmarket20.R;
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport;

public class ProductEditLayout4 extends ProductEditLayoutCommodity {

    public ProductEditLayout4(Context context) {
        this(context,null);
    }

    public ProductEditLayout4(Context context, AttributeSet attrs) {
        this(context, attrs,0);
    }

    public ProductEditLayout4(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void initViews() {
        View view = View.inflate(getContext(), R.layout.product_edit_layout4, this);
        iv_numSub = view.findViewById(R.id.iv_numSub);
        iv_numAdd = view.findViewById(R.id.iv_numAdd);
        tv_number = view.findViewById(R.id.tv_number);
        fl_root_view = view.findViewById(R.id.rl_layout);
        rtvAddCart = view.findViewById(R.id.rtvAddCart);
    }

    @Override
    public void onAddCartClick() {
        super.onAddCartClick();
        CommodityDetailReport.bottomBtnClickRight(getContext(), 1, rtvAddCart.getText().toString());
    }
}
