package com.ybmmarket20.activity

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.payment.VirtualGoldRechargeTextBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import java.text.DecimalFormat

/**
 * @class   ShoppingGoldRechargeResultActivity
 * <AUTHOR>
 * @date  2025/3/3
 * @description  购物金在线充值结果页
 */
@Router("shoppinggoldrechargeresult")
class ShoppingGoldRechargeResultActivity: BaseActivity(),View.OnClickListener{

    private var tips = ""
    private var resultType = "" //1 展示我的红包 2 展示我的优惠券
    private var mResultTextList = arrayListOf<VirtualGoldRechargeTextBean>()
    private var mAmount = ""

    private lateinit var tvLeft:TextView
    private lateinit var tvMyShoppingGold:TextView
    private lateinit var clRedPack:ConstraintLayout
    private lateinit var tvContent:TextView
    private lateinit var tvPrice:TextView

    companion object{
        private const val MY_SHOPPING_GOLD = "我的购物金"
        private const val MY_COUPON = "我的优惠券"
        private const val MY_RED_ENVELOPE = "我的红包"
        private const val INTENT_RESULT_TYPE = "intent_result_type"
        private const val INTENT_RECHARGE_TIPS = "intent_recharge_tips"
        private const val INTENT_AMOUNT = "intent_amount"

        fun launchActivity(mContext: Context,mAmount:String?="",resultType:String?="",resultTextList:ArrayList<VirtualGoldRechargeTextBean>?= arrayListOf()) {
            val intent = Intent(mContext, ShoppingGoldRechargeResultActivity::class.java).apply {
                putExtra(INTENT_RESULT_TYPE, resultType)
                putExtra(INTENT_RECHARGE_TIPS, resultTextList)
                putExtra(INTENT_AMOUNT, mAmount)
            }
            mContext.startActivity(intent)
        }
    }


    override fun getContentViewId(): Int = R.layout.activity_shopping_gold_recharge_result

    private fun formatAmount(mAmount: String): String? {
        return try {
            // 将字符串转换为 double 类型
            val amount = mAmount.toDouble()
            // 使用 DecimalFormat 格式化数字，保留两位小数
            val decimalFormat = DecimalFormat("0.00")
            decimalFormat.format(amount)
        } catch (e: NumberFormatException) {
            // 处理无法转换为数字的情况
            "" // 或者你可以选择抛出异常或者返回其他默认值
        }
    }
    override fun initData() {
        tvLeft = findViewById(R.id.tv_left)
        tvMyShoppingGold = findViewById(R.id.tv_my_shopping_gold)
        clRedPack = findViewById(R.id.cl_red_pack)
        tvContent = findViewById(R.id.tv_content)
        tvPrice = findViewById(R.id.tv_price)
        tvLeft.setOnClickListener(this)
        tvMyShoppingGold.setOnClickListener(this)

        setTitle("购物金在线充值")
        intent.extras?.let { bundle->
            resultType = bundle.getString(INTENT_RESULT_TYPE)?:""
            mResultTextList = bundle.getParcelableArrayList(INTENT_RECHARGE_TIPS)?: arrayListOf()
            mAmount = bundle.getString(INTENT_AMOUNT)?: ""
        }

        mAmount = formatAmount(mAmount)?:""

        if (mAmount.isNotEmpty()){
            val builder = SpannableStringBuilder("￥${mAmount}")
            builder.setSpan(AbsoluteSizeSpan(24, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            tvPrice.text = builder
        }

        val spannableStringBuilder = SpannableStringBuilder()
        if (mResultTextList.isNotEmpty()){

            for (bean in mResultTextList) {
                if (bean.text != null && bean.text.isNotEmpty()) {
                    val mText = bean.text
                    var mColor = Color.BLACK
                    try {
                        if (bean.color != null && bean.color.isNotEmpty()) {
                            mColor = Color.parseColor(bean.color)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    spannableStringBuilder.append(mText)
                    spannableStringBuilder.setSpan(
                            ForegroundColorSpan(mColor),
                            0,
                            mText.length,
                            0
                    )
                }
            }
        }

        if (spannableStringBuilder.isEmpty()){
            clRedPack.visibility = View.GONE
        }else{
            clRedPack.visibility = View.VISIBLE
            tvContent.text = spannableStringBuilder
        }


        when(resultType){
            "1" ->{
                tvLeft.visibility = View.VISIBLE
                tvLeft.text = MY_RED_ENVELOPE
            }

            "2" ->{
                tvLeft.visibility = View.VISIBLE
                tvLeft.text = MY_COUPON
            }

            else ->{
                tvLeft.visibility = View.GONE
            }
        }

    }

    override fun onClick(v: View?) {
        v?:return
        when(v.id){
            R.id.tv_left ->{
                v as TextView

                when(v.text){
                    MY_COUPON->{ //跳我的优惠券
                        RoutersUtils.open("ybmpage://couponmeber")
                        finish()
                    }

                    MY_RED_ENVELOPE->{ //跳我的红包
                        RoutersUtils.open("ybmpage://myredenvelope")
                        finish()
                    }
                }
            }

            R.id.tv_my_shopping_gold ->{//跳我的购物金
                RoutersUtils.open("ybmpage://myvirtualmoney")
                finish()
            }
        }
    }
}