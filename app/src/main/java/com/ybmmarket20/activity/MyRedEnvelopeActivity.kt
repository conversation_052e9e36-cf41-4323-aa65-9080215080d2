package com.ybmmarket20.activity

import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.fragments.MyRedEnvelopeRecordTabFragment
import com.ybmmarket20.fragments.MyRedEnvelopeTabFragment
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.viewmodel.RedEnvelopeViewModel
import kotlinx.android.synthetic.main.activity_red_envelope.*

/**
 * 红包
 */
@Router("myredenvelope", "myredenvelope/:totalAmount")
class MyRedEnvelopeActivity: BaseActivity() {

    var mCurrentFragment: Fragment = MyRedEnvelopeTabFragment()
    var fragmentType = 0
    val mViewModel: RedEnvelopeViewModel by viewModels()

    override fun getContentViewId(): Int = R.layout.activity_red_envelope

    override fun initData() {
        setTitle("红包")
        setRigthText({
            RoutersUtils.open(getUrl())
            XyyIoUtil.track("action_Me_RedPacket_instruction", hashMapOf(
                    "name" to "红包使用说明",
                    "action" to getUrl()
            ))
        }, "使用说明")
//        tv_red_envelope_amount.text = UiUtils.transform(intent.getStringExtra("totalAmount"))
        mViewModel._redEnvelopeLiveData.observe(this, Observer {
            if (it?.result?.balance != null) {
                tv_red_envelope_amount.text = UiUtils.transform(it.result!!.balance)
            }
        })
        mViewModel.getRedEnvelopeList("1", "1", "")
        val tempFragment = MyRedEnvelopeTabFragment()
        switchFragment(tempFragment)
        mCurrentFragment = tempFragment
        rg_class_selector.setOnCheckedChangeListener { _, radioButtonId ->
            val fragment = when(radioButtonId) {
                R.id.rb_my_red_envelope -> {
                    if (fragmentType == 0) null
                    else {
                        fragmentType = 0
                        MyRedEnvelopeTabFragment()
                    }
                }
                R.id.rb_my_red_envelope_record -> {
                    if (fragmentType == 1) null
                    else {
                        fragmentType = 1
                        MyRedEnvelopeRecordTabFragment()
                    }
                }
                else -> null
            }
            fragment?.let { switchFragment(it) }
        }
    }

    /**
     * 切换tab
     */
    private fun switchFragment(toFragment: Fragment) {
        val trackName = when (toFragment) {
            is MyRedEnvelopeTabFragment -> "我的红包"
            is MyRedEnvelopeRecordTabFragment -> "收支记录"
            else -> ""
        }
        XyyIoUtil.track("action_Me_RedPacket_menu1", hashMapOf(
                "name" to trackName
        ))
        val ft = supportFragmentManager.beginTransaction()
        ft.remove(mCurrentFragment)
        ft.replace(R.id.fl_red_envelope, toFragment).commit()
        mCurrentFragment = toFragment
    }

    fun getUrl(): String {
        return "ybmpage://commonh5activity?url=${AppNetConfig.getStaticHost2HttpsNewStatic()}#/redEnvelope/rulesOfUse?ybm_title=红包使用说明&head_menu=0"
    }
}