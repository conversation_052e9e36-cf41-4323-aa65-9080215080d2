package com.ybmmarket20.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartVoucher
import com.ybmmarket20.bean.GiftSubTotalResponseBean
import com.ybmmarket20.bean.ManufacturersBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.SearchGiftSelectResponseBean
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.search.BaseSearchProductActivity
import com.ybmmarket20.search.SpecficationPopWindow
import com.ybmmarket20.search.SynthesizePopWindow
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.view.AllProductPopWindowV2
import com.ybmmarket20.view.BaseFilterPopWindow
import com.ybmmarket20.view.Manufacturers2Pop
import com.ybmmarket20.view.couponrelatedgoods.CouponFilterClassify
import com.ybmmarket20.viewmodel.BaseViewModel
import com.ybmmarket20.viewmodel.GiftSelectGoodsViewModel
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import kotlinx.android.synthetic.main.activity_gift_select_goods.brand_rg_01
import kotlinx.android.synthetic.main.activity_gift_select_goods.etSearch
import kotlinx.android.synthetic.main.activity_gift_select_goods.ivEtClear
import kotlinx.android.synthetic.main.activity_gift_select_goods.rb_all_category
import kotlinx.android.synthetic.main.activity_gift_select_goods.rv_gift
import kotlinx.android.synthetic.main.activity_gift_select_goods.tvToCart
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_amount
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_gift_activity_content
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_gift_tips
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_gift_total
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_manufacturer
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_shop
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_specification
import kotlinx.android.synthetic.main.activity_gift_select_goods.tv_synthesize

/**
 * 赠品选择凑单页
 *
 * ybmpage://shopGiftCollect?promoId=promoId&bizSource=bizSource
 */

@Router("shopGiftCollect")
class GiftSelectGoodsActivity: BaseActivity() {

    private val mViewModel: GiftSelectGoodsViewModel by viewModels()
    private var mAdapter: GoodListAdapterNew? = null
    private val mGoodsList = mutableListOf<RowsBean>()
    private lateinit var broadcastReceiver: BroadcastReceiver

    //查赠品池所需的id
    private var promoId: String? = ""

    private var searchFilterSynthesizeBean: SearchFilterBean? = null

    //规格列表
    private var specBeans: MutableList<SearchFilterBean> = ArrayList()
    private var specStr = ""

    //厂家列表
    private var manufacturerList: ArrayList<ManufacturersBean> = ArrayList()
    private var manufacturerSelectList = arrayListOf<String>()

    protected var id = "" //商品分类过来的

    private val synthesizePopWindow: SynthesizePopWindow by lazy {
        SynthesizePopWindow().apply {
            setOnSelectListener(object : BaseFilterPopWindow.OnSelectListener {
                override fun getValue(show: SearchFilterBean) {
                    searchFilterSynthesizeBean = show
                    if (show == null) return
                    tv_synthesize.setText(show.showSynthesizeStr)
                }

                override fun OnDismiss(multiSelectStr: String) {
                    setRightDrawable(tv_synthesize, R.drawable.icon_navigation_arrow_down)
                    getGoodsListByAppendParam(hashMapOf())
                }
            })
        }
    }

    private val specficationPopWindow: SpecficationPopWindow by lazy {
        SpecficationPopWindow(specBeans).apply {
            setOnSelectListener(object : BaseFilterPopWindow.OnSelectListener {
                override fun getValue(show: SearchFilterBean) {
                    // 此选项为多选，因此在ondissmiss中直接获取最终选择结果
                }

                override fun OnDismiss(multiSelectStr: String) {
                    setIconState(tv_specification, R.drawable.icon_navigation_arrow_down)
                    if (!TextUtils.equals(specStr, multiSelectStr)) {
                        specStr = multiSelectStr
                        getGoodsListByAppendParam(hashMapOf())
                    }

                    setFilterBtnTextColor()
                }
            })
        }
    }

    private val mPopManufacturer: Manufacturers2Pop by lazy {
        Manufacturers2Pop().apply {
            setOnSelectListener(object : BaseFilterPopWindow.OnSelectListener {
                override fun getValue(bean: SearchFilterBean) {
                    setManufacturer(bean)
                    getGoodsListByAppendParam(hashMapOf())
                }

                override fun OnDismiss(multiSelectStr: String) {
                    setManufacturersState()
                }
            })
        }
    }

    private val mPopWindowProduct: AllProductPopWindowV2 by lazy {
        AllProductPopWindowV2().apply {
            setOnSelectListener(object : AllProductPopWindowV2.OnSelectListener {
                override fun getValue(categoryIds: Set<String>?) {
                    if (categoryIds == null) return
                    val builder = StringBuilder()
                    for (categoryId in categoryIds) {
                        builder.append(categoryId).append(",")
                    }
                    id = builder.toString()
                    if (!TextUtils.isEmpty(id)) {
                        id = id.substring(0, id.length - 1)
                    }
                }

                override fun OnDismiss(multiSelectStr: String) {
                    rb_all_category.setActivated(!TextUtils.isEmpty(id))
                    setIconState(rb_all_category, R.drawable.manufacturers_def)
                    getGoodsListByAppendParam(hashMapOf())
                }
            })

        }
    }

    private val mClassifyPop2: CouponFilterClassify by lazy {
        CouponFilterClassify().apply {
            init()
            setOnSelectListener(object: CouponFilterClassify.ISelectListener{
                override fun onDismiss(params: Map<String, Boolean>) {
                    if (params.isNotEmpty()){
                        tv_shop.isActivated = true
                        setIconState(rb_all_category, R.drawable.manufacturers_def)
                    }else{
                        tv_shop.isActivated = false
                        setIconState(tv_shop, R.drawable.manufacturers_checked_green)
                    }
                }

                override fun onResult(params: Map<String, String>) {
                    getGoodsListByAppendParam(params as HashMap<String, String>)
                }
            })
        }
    }


    override fun getContentViewId(): Int = R.layout.activity_gift_select_goods

    override fun onCreate(savedInstanceState: Bundle?) {
        //防止进入时EditText自动获取焦点弹出键盘
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN or WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        super.onCreate(savedInstanceState)
    }

    override fun initData() {
        promoId = intent.getStringExtra("promoId")
        initObserver()
        initRv()
        initSearchEdit()
        initReceiver()
        initGoCart()
        initGetSubTotal()
//        crgFilter.apply {
//            //跨店券指定店铺显示商家筛选
//            setItemList(if (isDesignateShop) getFilterItemList() else getFilterItemListWithoutBusiness(), voucherTemplateId, isDesignateShop)
//            setOnResultCallback(::getGoodsListByAppendParam)
//        }
    }

    private fun initGetSubTotal() {
        mViewModel.getSubTotal(
                hashMapOf(
                        Pair("promoId", promoId ?: ""),
                        Pair("merchantId", SpUtil.getMerchantid())
                )
        )
    }

    private fun initGoCart() {
        tvToCart.setOnClickListener {
            RoutersUtils.open("ybmpage://main?tab=2&name=凑单页&id=${RoutersUtils.encodeRAWUrl("ybmpage://shopGiftCollect?promoId=${promoId}")}")
        }
    }

    /**
     * 设置厂家筛选后各个按钮的状态
     */
    private fun setManufacturersState() {
        tv_manufacturer.isActivated = manufacturerSelectList.size>0
        setIconState(tv_manufacturer, R.drawable.manufacturers_def)
    }

    private fun initSearchEdit() {
        etSearch.doAfterTextChanged {
            ivEtClear.visibility = if (it.toString().isEmpty()) View.GONE else View.VISIBLE
        }
        ivEtClear.setOnClickListener {
            etSearch.setText("")
            getGoodsListByAppendParam(hashMapOf(
                    "queryWord" to ""
            ))
        }
        etSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                getGoodsListByAppendParam(hashMapOf(
                        "queryWord" to etSearch.text.toString()
                ))
                hideSoftInput()
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }
    }

    private fun initRv() {
        mAdapter = GoodListAdapterNew(R.layout.item_goods_new, mGoodsList)
        mAdapter?.setEmptyView(this, R.layout.layout_empty_view, R.drawable.icon_empty, "暂无商品")
        rv_gift.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rv_gift.adapter = mAdapter
        mAdapter?.isHiddenPromotionMore = true
        mAdapter?.setEnableLoadMore(true)
        mAdapter?.setOnLoadMoreListener({
            getGoodsListByLoadMore()
        }, rv_gift)
        getGoodsListByAppendParam(hashMapOf("promoId" to (promoId ?: "")), true)
    }

    private fun setFilterBtnTextColor() {
        tv_specification.isActivated = !TextUtils.isEmpty(specStr)
    }

    private fun initObserver() {
        tv_synthesize.setOnClickListener {
            if (!synthesizePopWindow.isShow()) {
                setRightDrawable(tv_synthesize, R.drawable.icon_navigation_arrow_up)
            }
            synthesizePopWindow.show(brand_rg_01)
        }

        tv_specification.setOnClickListener {
            hideSoftInput()
            tv_specification.isActivated = true
            setIconState(tv_specification, R.drawable.manufacturers_checked_green)
            specficationPopWindow.show(brand_rg_01)
        }

        tv_manufacturer.setOnClickListener {
            hideSoftInput()
            tv_manufacturer.isActivated = true
            setIconState(tv_manufacturer, R.drawable.manufacturers_checked_green)
            mPopManufacturer.show(brand_rg_01, manufacturerList)
        }

        rb_all_category.setOnClickListener {
            hideSoftInput()
            rb_all_category.isActivated = true
            setIconState(rb_all_category, R.drawable.manufacturers_checked_green)
            mPopWindowProduct.show(rb_all_category, id)
        }

        tv_shop.setOnClickListener {
            hideSoftInput()
            tv_shop.isActivated = true
            setIconState(tv_shop, R.drawable.manufacturers_checked_green)
            mClassifyPop2.show()
        }

        //商品列表
        mViewModel.giftSelectGoodsListLiveData.observe(this, ::handleGoodsList)
        //商品列表-加载更多
        mViewModel.loadMoreGoodsListLiveData.observe(this, ::handleLoadMoreGoodsList)
        //获取小计
        mViewModel.subTotalsLiveData.observe(this, ::handleSubTotal)
    }

    /**
     * 设置厂家过来的数据
     *
     * @param bean
     */
    private fun setManufacturer(bean: SearchFilterBean) {
        manufacturerSelectList.clear()
        manufacturerSelectList.addAll(bean.lastNames)

        if (manufacturerSelectList.isEmpty()) {
            tv_manufacturer.isActivated = false
            setIconState(tv_manufacturer, R.drawable.manufacturers_def)
        }else{
            tv_manufacturer.isActivated = true
            setIconState(tv_manufacturer, R.drawable.manufacturers_checked_green)
        }
    }

    /**
     * 处理商品列表加载逻辑
     */
    private fun handleGoodsList(responseBean: BaseBean<SearchGiftSelectResponseBean>) {
        if (responseBean.isSuccess && responseBean.data != null) {
            mGoodsList.clear()
            mGoodsList.addAll(responseBean.data.dataList)
            mAdapter = GoodListAdapterNew(R.layout.item_goods_new, mGoodsList).apply {
                isHiddenPromotionMore = true
                setEmptyView(this@GiftSelectGoodsActivity, R.layout.layout_empty_view, R.drawable.icon_empty, "暂无商品")
                rv_gift.adapter = this
                setEnableLoadMore(true)
                setOnLoadMoreListener({ getGoodsListByLoadMore() }, rv_gift)
                AdapterUtils.getAfterDiscountPrice(responseBean.data.dataList.toMutableList(),
                        this, true)
                notifyDataChangedAfterLoadMore(!responseBean.data.isEnd)
            }

            handlePromoDesc(responseBean.data.promoDesc)
            handleSpec(responseBean.data?.filterConditions?.specList ?: arrayListOf())
            handleManufacturer(responseBean.data?.filterConditions?.manufacturerList
                    ?: arrayListOf())
        }
        dismissProgress()
    }

    private fun handleManufacturer(manufacturerList: ArrayList<ManufacturersBean>) {
        this.manufacturerList = manufacturerList
    }

    /**
     * 处理活动描述
     */
    private fun handlePromoDesc(promoDesc: String?) {
        tv_gift_activity_content.apply {
            text = promoDesc ?: ""
            gravity = if (lineCount>1){
                Gravity.CENTER_VERTICAL
            }else{
                Gravity.CENTER
            }
        }
    }

    /**
     * 反聚规格列表
     * @param specList ArrayList<String?>
     */
    private fun handleSpec(specList: ArrayList<SearchFilterBean?>) {
        specBeans = specList as MutableList<SearchFilterBean>
    }

    private fun handleSubTotal(mBean:BaseBean<GiftSubTotalResponseBean>) {
        mBean.data?.apply {
            tv_gift_tips.isVisible = !(title.isNullOrEmpty())
            tv_gift_tips.text = title?:""

            tv_amount.text = "小计：¥${subTotal?:""}"
            tv_gift_total.text = "共${countTotal?:""}件商品"
        }
    }

    /**
     * 处理商品列表加载更多逻辑
     */
    private fun handleLoadMoreGoodsList(responseBean: BaseBean<SearchGiftSelectResponseBean>) {
        if (responseBean.isSuccess && responseBean.data != null && responseBean.data.rows != null) {
            mGoodsList.addAll(responseBean.data.dataList)
            mAdapter?.notifyDataChangedAfterLoadMore(!responseBean.data.isEnd)
            mAdapter?.let {
                AdapterUtils.getAfterDiscountPrice(responseBean.data.dataList.toMutableList(),
                        it, true)
            }
        } else {
//            mAdapter?.notifyDataChangedAfterLoadMore(false)
            mAdapter?.loadMoreFail()
        }
    }

    /**
     * 通过追加参数获取商品列表
     */
    private fun getGoodsListByAppendParam(params: HashMap<String, String>, isFirstRequest: Boolean = false) {
        showProgress(false)

        // 综合、销量、价格 排序
        searchFilterSynthesizeBean?.apply {
            val saleProperty = when (selectedSearchOption) {
                SearchFilterBean.SALESVOLUME -> BaseSearchProductActivity.PROPERTY_FILTER_SALE_VOLUME_V2
                SearchFilterBean.PRICE -> BaseSearchProductActivity.PROPERTY_FILTER_PRICE_V2
                else -> BaseSearchProductActivity.PROPERTY_FILTER_DEFAULT_V2
            }
            params["sortStrategy"] = saleProperty
        } ?: kotlin.run {
            params["sortStrategy"] = BaseSearchProductActivity.PROPERTY_FILTER_DEFAULT_V2
        }

        // 规格
        params["specs"] = if (!TextUtils.isEmpty(specStr)) {
            val specArr = specStr.split(",")
            Gson().toJson(specArr).toString()
        } else {
            ""
        }

        //厂家
        params["manufacturers"] = if (manufacturerSelectList.size >0){
            Gson().toJson(manufacturerSelectList).toString()
        }else{
            ""
        }

        //分类
        params["categoryIdsStr"] = id

        mViewModel.getGoodsListByAppendParam(params, isFirstRequest)
    }

    /**
     * 获取商品列表-加载更多
     */
    private fun getGoodsListByLoadMore() {
        mViewModel.getGoodsListByLoadMore()
    }

    /**
     * 初始化广播接收器
     */
    private fun initReceiver() {
        broadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent?) {
                intent?.takeIf { it.action == IntentCanst.ACTION_ADD_PRODUCT
                        || it.action == IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON
                        || it.action == IntentCanst.ACTION_RELATED_GOODS_ADD_GOODS
                        || it.action == IntentCanst.ACTION_SHOPNUMBER}?.let {
                    getGoodsListByAppendParam(hashMapOf("promoId" to (promoId ?: "")), true)
                    initGetSubTotal()
                }
            }
        }
        LocalBroadcastManager.getInstance(applicationContext).registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(IntentCanst.ACTION_ADD_PRODUCT)
            addAction(IntentCanst.ACTION_RELATED_GOODS_ADD_GOODS)
            addAction(IntentCanst.ACTION_ADD_PRODUCT_FROM_DETAIL_TO_COUPON)  // 接收详情页加购消息更新小计
            addAction(IntentCanst.ACTION_SHOPNUMBER)
        })
    }

    override fun getBaseViewModel(): BaseViewModel = mViewModel

    override fun onDestroy() {
        super.onDestroy()
        LocalBroadcastManager.getInstance(this).unregisterReceiver(broadcastReceiver)
    }

    /**
     * 设置箭头指示状态 →，→
     *
     * @param tv
     * @param id
     */
    private fun setIconState(tv: TextView?, id: Int) {
        try {
            if (tv == null) return
            val nav_up = resources.getDrawable(id)
            nav_up.setBounds(0, 0, nav_up.minimumWidth, nav_up.minimumHeight)
            tv.setCompoundDrawables(null, null, nav_up, null)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setRightDrawable(textView: TextView, drawableRes: Int) {
        val drawable = resources.getDrawable(drawableRes)
        // 这一步必须要做,否则不会显示.
        drawable.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
        textView.setCompoundDrawables(null, null, drawable, null)
    }

}