package com.ybmmarket20.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 扫描页面-补货登记
 */
@Router({"replenishmentprogram", "replenishmentprogram/:replenishmentprogram_code"})
public class ReplenishmentProgramActivity extends BaseActivity {

    @Bind(R.id.iv_back)
    ImageView mIvBack;
    @Bind(R.id.tv_title)
    TextView mTvTitle;
    @Bind(R.id.tv_right)
    TextView mTvRight;
    @Bind(R.id.iv_right)
    ImageView mIvRight;
    @Bind(R.id.ll_title)
    RelativeLayout mLlTitle;
    @Bind(R.id.tv_name)
    EditText mTvName;
    @Bind(R.id.tv_specification)
    EditText mTvSpecification;
    @Bind(R.id.tv_manufacturers)
    EditText mTvManufacturers;
    @Bind(R.id.tv_number)
    EditText mTvNumber;
    @Bind(R.id.btn_ok)
    Button mBtnOk;

    private String planningScheduleId;

    @Override
    protected void initData() {
        setTitle("补货登记");
        planningScheduleId = getIntent().getStringExtra(IntentCanst.REPLENISHMENTPROGRAM_CODE);

        mTvNumber.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEND
                        || actionId == EditorInfo.IME_ACTION_DONE
                        || (event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode()
                        && KeyEvent.ACTION_DOWN == event.getAction())) {
                    String num = mTvNumber.getText().toString().trim();
                    if (TextUtils.isEmpty(num) || Integer.parseInt(num) < 1) {
                        ToastUtils.showShort("登记数据应大于0");
                        return true;
                    }
                    if (!TextUtils.isEmpty(planningScheduleId)) {
                        addReplenishmentProgram(planningScheduleId);
                    }
                    return true;
                }
                return false;
            }
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_replenishment_program;
    }

    @OnClick({R.id.btn_ok})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.btn_ok:
                if (!TextUtils.isEmpty(planningScheduleId)) {
                    addReplenishmentProgram(planningScheduleId);
                }
                break;
        }
    }

    /*
    * 添加补货登记
    * */
    private void addReplenishmentProgram(String planningScheduleId) {

        String productNameHint = "请输入产品名称";
        String specHint = "请输入规格";
        String manufacturerHint = "请输入厂家";
        String purchaseNumberHint = "登记数量应大于0";

        String productName = mTvName.getText().toString().trim();
        String spec = mTvSpecification.getText().toString().trim();
        String manufacturer = mTvManufacturers.getText().toString().trim();
        final String purchaseNumber = mTvNumber.getText().toString().trim();

        if (TextUtils.isEmpty(productName)) {
            ToastUtils.showShort(productNameHint);
            return;
        }
        if (TextUtils.isEmpty(spec)) {
            ToastUtils.showShort(specHint);
            return;
        }
        if (TextUtils.isEmpty(manufacturer)) {
            ToastUtils.showShort(manufacturerHint);
            return;
        }
        if (TextUtils.isEmpty(purchaseNumber) || Integer.parseInt(purchaseNumber) < 1) {
            ToastUtils.showShort(purchaseNumberHint);
            return;
        }
        RequestParams params = RequestParams.newBuilder().url(AppNetConfig.REPLENISHMENT_ADD_PRODUCTTOPLAN)
                .addParam("manufacturer", manufacturer)
                .addParam("productName", productName)
                .addParam("spec", spec)
                .addParam("purchaseNumber", purchaseNumber)
                .addParam("planningScheduleId", planningScheduleId)
                .addParam("merchantId", HttpManager.getInstance().getMerchant_id()).build();

        HttpManager.getInstance().post(params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean bean) {
                if(obj!=null && obj.isSuccess()) {
                    ToastUtils.showShort("添加成功");
                    Intent intent = new Intent();
                    Bundle bundle = new Bundle();
                    bundle.putString(IntentCanst.REPLENISHMENTPROGRAM_NUMBER, purchaseNumber);
                    intent.putExtras(bundle);
                    setResult(RESULT_OK, intent);
                    finish();
                }
            }

            @Override
            public void onFailure(NetError error) {
                if (!TextUtils.isEmpty(error.message)) {
                    ToastUtils.showShort(error.message);
                }
            }
        });

    }
}
