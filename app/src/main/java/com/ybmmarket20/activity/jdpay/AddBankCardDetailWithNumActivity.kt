package com.ybmmarket20.activity.jdpay

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.Base64
import android.view.View
import androidx.activity.viewModels
import androidx.core.widget.addTextChangedListener
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.bean.BankCardInfo
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.AddBankCardDetailWithNumViewModel
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.etUserIdCard
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.etUserName
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.ivUserIdCardClear
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.ivUserNameClear
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.rtvNext
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.tvPrivacy
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.tvUserIdCardTitleTips
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.tvUserNameTitleTips
import kotlinx.android.synthetic.main.activity_add_bank_card_detail_with_num.*

/**
 * 通过卡号添加银行卡
 */
@Router("addbankcarddetailwithnum")
class AddBankCardDetailWithNumActivity: BaseActivity(), View.OnClickListener {

    private val mViewModel: AddBankCardDetailWithNumViewModel by viewModels()
    var validateUserName = false
    var validateUserIDCardNum = false
    var mCardType = ""
    var mBankCode: String? = ""
    var mBankName = ""
    var bankCarNo = ""

    override fun getContentViewId(): Int = R.layout.activity_add_bank_card_detail_with_num

    override fun initData() {
        setTitle("添加银行卡")
        try {
            val bankCarInfoJson = String(Base64.decode(intent.getStringExtra("bankCardInfo"), Base64.URL_SAFE))
            val bankCardInfo = Gson().fromJson(bankCarInfoJson, BankCardInfo::class.java)
            handleBankCardInfo(bankCardInfo)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        setObserver()
        showProgress()
        mViewModel.queryIdentityInfo()
        rtvNext.setOnClickListener(this)
        ivUserNameClear.setOnClickListener(this)
        ivUserIdCardClear.setOnClickListener(this)
        ivUserMobileClear.setOnClickListener(this)
        etUserName.addTextChangedListener(afterTextChanged = {
            if (validateUserName) return@addTextChangedListener
            ivUserNameClear.visibility = if (it.isNullOrEmpty()) View.GONE else View.VISIBLE
        })
        etUserIdCard.addTextChangedListener(afterTextChanged = {
            if (validateUserIDCardNum) return@addTextChangedListener
            ivUserIdCardClear.visibility = if (it.isNullOrEmpty()) View.GONE else View.VISIBLE
        })
        etUserMobile.addTextChangedListener(afterTextChanged = {
            ivUserMobileClear.visibility = if (it.isNullOrEmpty()) View.GONE else View.VISIBLE
        })
    }

    private fun setObserver() {
        mViewModel.identityInfoLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (!TextUtils.isEmpty(it.data?.name)) {
                    etUserName.isFocusable = false
                    etUserName.setText(it.data.name)
                    validateUserName = true
                    ivUserNameClear.visibility = View.GONE
                    etUserName.setBackgroundResource(R.drawable.shape_add_bank_card_bg)
                }
                if (!TextUtils.isEmpty(it.data?.certId)) {
                    etUserIdCard.isFocusable = false
                    etUserIdCard.setText(it.data.certId)
                    validateUserIDCardNum = true
                    ivUserIdCardClear.visibility = View.GONE
                    etUserIdCard.setBackgroundResource(R.drawable.shape_add_bank_card_bg)
                }
            }
        }

        mViewModel.applyBindCardWithNumLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (!TextUtils.isEmpty(it.data.contractNo) && it.data != null) {
                    it.data.bankName = mBankName
                    val jsonStr = Gson().toJson(it.data)
                    val params = Base64.encodeToString(jsonStr.toByteArray(), Base64.URL_SAFE)
                    RoutersUtils.open("ybmpage://checkreservephonenum?params=$params")
                }
            }
        }
    }

    /**
     * 处理上个页面传过来的信息
     */
    private fun handleBankCardInfo(bankInfo: BankCardInfo) {
        mCardType = bankInfo.cardType?: ""
        mBankCode = bankInfo.bankCode?: ""
        mBankName = bankInfo.bankName?: ""
        bankCarNo = bankInfo.bankCardNo?: ""
        ImageUtil.load(this, bankInfo.bankLogo, ivLogo)
        tvBankName.text = "${bankInfo.bankName}${bankInfo.cardType} (${bankInfo.bankCardNo?.substring(((bankInfo.bankCardNo?.length)?: 4) - 4)})"
        val builder = SpannableStringBuilder("请仔细阅读,")
        val privacyBuilder = SpannableStringBuilder(bankInfo.agreementDesc)
        privacyBuilder.setSpan(ForegroundColorSpan(Color.parseColor("#00B377")), 0, privacyBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        builder.append(privacyBuilder)
        tvPrivacy.text = builder
        tvPrivacy.setOnClickListener {
            val url = if(TextUtils.isEmpty(bankInfo.agreementUrl)) {
                ""
            } else if (bankInfo.agreementUrl!!.contains("?")){
                "${bankInfo.agreementUrl}&ybm_title=${bankInfo.agreementDesc}"
            } else {
                "${bankInfo.agreementUrl}?ybm_title=${bankInfo.agreementDesc}"
            }
            RoutersUtils.open("ybmpage://commonh5activity?url=$url&isShowCart=0")
        }
    }

    private fun checkAndSubmit() {
        tvUserNameTitleTips.text = ""
        tvUserIdCardTitleTips.text = ""
        tvUserMobileTitleTips.text = ""
        val userName = etUserName.text.toString()
        val idCardNum = etUserIdCard.text.toString()
        val userMobile = etUserMobile.text.toString()
        if (TextUtils.isEmpty(userName)) {
            tvUserNameTitleTips.text = "请输入持卡人姓名"
            ToastUtils.showShort("请输入持卡人姓名")
        } else if (TextUtils.isEmpty(idCardNum)) {
            tvUserIdCardTitleTips.text = "请输入身份证号"
            ToastUtils.showShort("请输入身份证号")
        } else if (idCardNum.length != 15 && idCardNum.length != 18) {
            tvUserIdCardTitleTips.text = "请输入15或18位身份证号"
            ToastUtils.showShort("请输入15或18位身份证号")
        } else if(TextUtils.isEmpty(userMobile)) {
            tvUserMobileTitleTips.text = "请输入预留手机号"
            ToastUtils.showShort("请输入预留手机号")
        } else if(userMobile.length < 11) {
            tvUserMobileTitleTips.text = "手机号输入错误，请检查"
            ToastUtils.showShort("手机号输入错误，请检查")
        } else {
            showProgress()
            val params = hashMapOf(
                "cardNo" to (bankCarNo?: ""),
                "cardType" to mCardType,
                "bankCode" to (mBankCode?: ""),
                "mobile" to userMobile,
                "bankName" to mBankName)
            if (!idCardNum.contains("*")) {
                params["idNo"] = idCardNum
                params["idName"] = userName
            }

            mViewModel.applyBindCardWithNum(params, bankCarNo?: "")
        }
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.ivUserNameClear -> {
                etUserName.setText("")
            }

            R.id.ivUserIdCardClear -> {
                etUserIdCard.setText("")
            }

            R.id.ivUserMobileClear -> {
                etUserMobile.setText("")
            }

            R.id.rtvNext -> {
                checkAndSubmit()
            }

        }
    }
}