package com.ybmmarket20.activity;

import android.content.Context;
import android.content.Intent;
import android.text.ClipboardManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.github.mzule.activityrouter.annotation.Router;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.quick.qt.commonsdk.QtConfigure;
import com.ybm.app.bean.DeviceEntity;
import com.ybm.app.common.SmartExecutorManager;
import com.ybmmarket20.R;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.DebugManager;
import com.ybmmarket20.common.ViewOnClickListener;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.db.info.HandlerGoodsDao;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.DebugDeviceWindow;
import com.ybmmarket20.xyyreport.EventReportManager;
import com.ybmmarket20.xyyreport.XyyReportManager;
import com.ybmmarket20.xyyreport.session.SessionManager;
import com.ybmmarket20.xyyreport.spm.RouterHelper;

import java.util.HashMap;
import java.util.Map;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 调试页面
 */
@Router("debugactivity")
public class DebugActivity extends BaseActivity implements View.OnClickListener{

    @Bind(R.id.tv_api)
    TextView tvApi;
    @Bind(R.id.cb_api)
    CheckBox cbApi;
    @Bind(R.id.ll_api)
    LinearLayout llApi;
    @Bind(R.id.tv_crash)
    TextView tvCrash;
    @Bind(R.id.cb_crash)
    CheckBox cbCrash;
    @Bind(R.id.ll_crash)
    LinearLayout llCrash;
    @Bind(R.id.tv_device)
    TextView tvDevice;
    @Bind(R.id.ll_device)
    LinearLayout llDevice;
    @Bind(R.id.ll_set_x5_inspect)
    LinearLayout llSetX5Inspect;
    @Bind(R.id.tv_get_qt_device_id)
    TextView tvQtDeviceId;
    @Bind(R.id.tv_get_qt_session)
    TextView tvQtSession;
    @Bind(R.id.smQtAllTrackSwitch)
    SwitchMaterial smQtAllTrackSwitch;
    @Bind(R.id.btnTrack)
    Button btnTrack;
    private boolean isSaveCrash = false;
    private boolean isSaveAPI = false;


    @Override
    protected void initData() {
        setTitle("开发者模式");
        isSaveCrash = DebugManager.getInstance().isSaveCrash();
        isSaveAPI =   DebugManager.getInstance().isSaveApi();
        cbApi.setChecked(isSaveAPI);
        cbCrash.setChecked(isSaveCrash);
        tvQtSession.setText("qtSession: " +SessionManager.get().getSession());
        tvQtDeviceId.setText("QT设备ID(点击复制到剪切板)：" +  QtConfigure.getUMIDString(this));
        smQtAllTrackSwitch.setChecked(SpUtil.readBoolean("qtAllTrackSwitch", false));
        smQtAllTrackSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            SpUtil.writeBoolean("qtAllTrackSwitch", isChecked);
            AlertDialogEx dialog = new AlertDialogEx(this);
            dialog.setMessage("重启后生效");
            dialog.setConfirmButton("重启", (AlertDialogEx.OnClickListener) (dialog1, button) -> {
                RoutersUtils.open("ybmaction://reboot/");
            }).setCancelButton("暂不重启", (dialog12, button) -> {
                dialog12.dismiss();
            }).show();
        });
    }

    @Override
    public int getContentViewId() {
        return R.layout.activity_debug;
    }


    @Override
    @OnClick({R.id.ll_device, R.id.ll_api, R.id.cb_api, R.id.ll_crash,R.id.cb_crash,R.id.ll_api_url, R.id.ll_set_x5_inspect, R.id.ll_get_qt_device_id, R.id.btnTrack})
    public void onClick(View v) {
        switch (v.getId()){
           case R.id.ll_device://查看设备信息
               DeviceEntity entity = new DeviceEntity(DebugActivity.this);
               DebugDeviceWindow deviceWindow = new DebugDeviceWindow();
               deviceWindow.show(llDevice,entity);
            break;
            case R.id.ll_api://查看接口数据
                startActivity(new Intent(getApplicationContext(),DebugAPIActivity.class));
                break;
            case R.id.cb_api://是否保存接口数据
                DebugManager.getInstance().setSaveApi(cbApi.isChecked());
                break;
            case R.id.ll_crash://查看应用崩溃
                break;
            case R.id.cb_crash://保存应用崩溃
                DebugManager.getInstance().setSaveCrash(cbCrash.isChecked());
                break;
            case R.id.title_left:
                finish();
                break;
            case R.id.ll_get_qt_device_id:
                ClipboardManager cm = (ClipboardManager)getSystemService(Context.CLIPBOARD_SERVICE);
                cm.setText(QtConfigure.getUMIDString(this));
                ToastUtils.showShort("已复制到剪切板");
                break;
            case R.id.ll_api_url:
                String api_url = AppNetConfig.getApiHost();
                String api_cdn = AppNetConfig.getCDNHost();
                String api_path = AppNetConfig.getApiPath();
                String api_static = AppNetConfig.getStaticHost2Https();
                AlertDialogEx dialogEx = new AlertDialogEx(this);
                View view = LayoutInflater.from(this).inflate(R.layout.set_api_url,null);
                final EditText etAPI = (EditText) view.findViewById(R.id.et_api);
                final EditText etCDN = (EditText) view.findViewById(R.id.et_cdn);
                final EditText etStatic = (EditText) view.findViewById(R.id.et_static);
                final EditText etPath = (EditText) view.findViewById(R.id.et_path);
                view.findViewById(R.id.btn_1).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        etAPI.setText(AppNetConfig.API_HOST_RELEASE);
                        etCDN.setText(AppNetConfig.CDN_HOST_RELEASE);
                        etPath.setText(AppNetConfig.API_PATH_RELEASE);
                        etStatic.setText(AppNetConfig.STATIC_HOST_RELEASE);
                    }
                });
                view.findViewById(R.id.btn_2).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        etAPI.setText(AppNetConfig.API_HOST_TEST);
                        etCDN.setText(AppNetConfig.CDN_HOST_TEST);
                        etPath.setText(AppNetConfig.API_PATH_TEST);
                        etStatic.setText(AppNetConfig.STATIC_HOST_TEST_HTTPS);
                    }
                });
                view.findViewById(R.id.btn_3).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        etAPI.setText(AppNetConfig.API_HOST_STAGE);
                        etCDN.setText(AppNetConfig.CDN_HOST_STAGE);
                        etPath.setText(AppNetConfig.API_PATH_STAGE);
                        etStatic.setText(AppNetConfig.STATIC_HOST_STAGE);
                    }
                });
                view.findViewById(R.id.btn_4).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        etAPI.setText(AppNetConfig.API_HOST_DEV);
                        etCDN.setText(AppNetConfig.CDN_HOST_DEV);
                        etPath.setText(AppNetConfig.API_PATH_DEV);
                        etStatic.setText(AppNetConfig.STATIC_HOST_DEV);
                    }
                });
                view.findViewById(R.id.btn_5).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        etAPI.setText(AppNetConfig.API_HOST_NEW_STAGE);
                        etCDN.setText(AppNetConfig.CDN_HOST_NEW_STAGE);
                        etPath.setText(AppNetConfig.API_PATH_NEW_STAGE);
                        etStatic.setText(AppNetConfig.STATIC_HOST_NEW_STAGE);
                    }
                });

//                ((Button) view.findViewById(R.id.btn_1)).setText("上线版本:"+AppNetConfig.API_HOST_RELEASE);
//                ((Button) view.findViewById(R.id.btn_2)).setText("阿里云测:"+AppNetConfig.API_HOST_TEST);
//                ((Button) view.findViewById(R.id.btn_3)).setText("预上线:"+AppNetConfig.API_HOST_STAGE);
//                ((Button) view.findViewById(R.id.btn_4)).setText("开发环境:"+AppNetConfig.API_HOST_DEV);
//                ((Button) view.findViewById(R.id.btn_5)).setText("新增预发:"+AppNetConfig.API_HOST_NEW_STAGE);

                etAPI.setText(api_url);
                etCDN.setText(api_cdn);
                etPath.setText(api_path);
                etStatic.setText(api_static);
                dialogEx.setContentView(view);
                dialogEx.setTitle("设置API");
                dialogEx.setCancelable(false);
                dialogEx.setAutoDismiss(false);
                etAPI.setSelected(true);
                etCDN.setSelected(true);
                dialogEx.setCancelButton("取消", new AlertDialogEx.OnClickListener() {
                    @Override
                    public void onClick(AlertDialogEx dialog, int button) {
                        dialog.dismiss();
                    }
                }).setConfirmButton("确认", new AlertDialogEx.OnClickListener() {
                    @Override
                    public void onClick(AlertDialogEx dialog, int button) {
                        if(TextUtils.isEmpty(etAPI.getText()) || TextUtils.isEmpty(etCDN.getText()) || TextUtils.isEmpty(etPath.getText())){
                            ToastUtils.showShort("请输入正确host");
                            return;
                        }
                        hideSoftInput();
                        AppNetConfig.setApiHost(etAPI.getText().toString().trim());
                        AppNetConfig.setApiPath(etPath.getText().toString().trim());
                        AppNetConfig.setCdnHost(etCDN.getText().toString().trim());
                        AppNetConfig.setStaticHost(etStatic.getText().toString().trim());
                        AppNetConfig.selectApiPingAnUrl(etAPI.getText().toString().trim());
                        HandlerGoodsDao.getInstance().deleteItems();//切换环境了，清空购物车数量
                        SmartExecutorManager.getInstance().executeUI(new Runnable() {
                            @Override
                            public void run() {
                                RoutersUtils.open("ybmaction://reboot/");
                            }
                        },800);
                    }
                }).show();
                break;

            case R.id.ll_set_x5_inspect:
                RoutersUtils.open("ybmpage://commonh5activity?url=http://debugx5.qq.com/");
                break;
            case R.id.btnTrack:
                clickCount ++;
                Map<String, Object> map = new HashMap<>();
                map.put("test_string", "123");
                map.put("test_int", clickCount);
                map.put("test_double", 2.22);
                map.put("test_string_array", new String[]{"0", "1", "2", "3"});
                map.put("spm_cnt", "<EMAIL>@1-4.5654f2feK9KIB2");
                EventReportManager.trackEvent(this, "test_create", map);
                break;
        }
    }
    private int clickCount = 0;
}
