package com.ybmmarket20.activity

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.BitmapFactory
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import butterknife.OnClick
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.tbruyelle.rxpermissions2.RxPermissions
import com.ybm.app.bean.NetError
import com.ybm.app.utils.PermissionDialogUtil
import com.ybmmarket20.R
import com.ybmmarket20.adapter.LicensePicListAdapter
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.ShowAptitudeBottomAddImageDialog
import com.ybmmarket20.view.jdaddressselector.AddressProvider
import com.ybmmarket20.view.jdaddressselector.BottomDialog
import com.ybmmarket20.view.jdaddressselector.OnAddressSelectedListener
import com.ybmmarket20.view.picker.PickerManager
import com.ybmmarket20.viewmodel.AssociateShopsViewModel
import kotlinx.android.synthetic.main.activity_register_add_shop.*
import java.io.File
import java.util.*

const val INTENT_DATA_ADD_SHOP_ADDRESS = "intent_data_add_shop_address"

@Router("registeraddshop")
class RegisterAddShopActivity : BaseActivity(), OnAddressSelectedListener {
    private var mCurrentProviceName = ""
    private var mCurrentCityName = ""
    private var mCurrentDistrictName = ""
    private var mCurrentStreetName = ""
    private var mCurrentProvinceId = ""
    private var mCurrentCityId = ""
    private var mCurrentDistrictId = ""
    private var mCurrentStreetId = ""
    private var dialog: BottomDialog? = null
    private val customerTypeList = mutableListOf<CustomerTypeBean>()
    private val firstCustomerType = 0//首次企业类型（客户类型） 默认单体药店
    private var mCustomerTypeIndex = 0 //默认选中客户类型
    private var mCustomerTypeId = 0 //客户类型id
    var codeType = true //true: 营业执照编码.false:医疗机构执业许可证编码
    var licenseImageUrl: String = ""
    var imageUrl: String? = null
    val mViewModel: AssociateShopsViewModel by viewModels()

    override fun getContentViewId(): Int {
        return R.layout.activity_register_add_shop
    }

    override fun initData() {
        setTitle("添加店铺")
        editChangeListener()
        val registerAddressBean: RegisterAddressBean? = intent.getSerializableExtra(INTENT_DATA_ADD_SHOP_ADDRESS) as RegisterAddressBean?
        initUi(registerAddressBean)
        setLeft { finish() }
    }

    private fun initUi(registerAddressBean: RegisterAddressBean?) {
        if (registerAddressBean != null) {
            register_shop.setText(registerAddressBean.shopName)
            register_et_address.setText(registerAddressBean.address)
            register_tv_address_title.setText(registerAddressBean.prefixAddress)
            mCurrentProviceName = registerAddressBean.province.toString()
            mCurrentProvinceId = registerAddressBean.provinceCode.toString()
            mCurrentCityName = registerAddressBean.city.toString()
            mCurrentCityId = registerAddressBean.cityCode.toString()
            mCurrentDistrictName = registerAddressBean.district.toString()
            mCurrentDistrictId = registerAddressBean.districtCode.toString()
            mCurrentStreetName = registerAddressBean.street.toString()
            mCurrentStreetId = registerAddressBean.streetCode.toString()
        }
    }

    private fun editChangeListener() {
        UiUtils.setEditTextInhibitInputSpace(register_et_address)
        register_btn_ensure.observer(register_et_address, register_shop, register_tv_address_title, etCompanyType, etNumber)
        register_btn_ensure.setOnItemClickListener { isFlag ->
            register_btn_ensure.isEnabled = isFlag && !imageUrl.isNullOrEmpty()
        }
    }

    @OnClick(R.id.register_tv_address_title, R.id.register_btn_ensure, R.id.ilCompanyType, R.id.etCompanyType, R.id.clAddImageBtn, R.id.iv_del)
    fun onClick(v: View?) {
        when (v?.id) {
            //选择4级地址
            R.id.register_tv_address_title -> {
                hideSoftInput()
                dialog = BottomDialog(this@RegisterAddShopActivity)
                dialog?.show()
                dialog?.setOnAddressSelectedListener(this@RegisterAddShopActivity)
                dialog?.setAddressProvider(addressListener)
            }
            R.id.register_btn_ensure -> {
                checkShopName()
            }

            R.id.ilCompanyType -> {
                hideSoftInput()
                showAptitudeDialog()
            }

            R.id.etCompanyType -> {
                hideSoftInput()
                showAptitudeDialog()
            }

            R.id.clAddImageBtn -> {
                PermissionDialogUtil.showPermissionInfoDialog(
                    this@RegisterAddShopActivity,
                    "药帮忙App需要申请存储权限和相机权限，用于拍照并存储照片"
                ) {
                    getRootPermissions()
                }
            }

            R.id.iv_del -> {
                switchImage(false, null)
            }
        }
    }

    /**
     * 获取6.0读取文件的权限
     */
    @SuppressLint("CheckResult")
    private fun getRootPermissions() {
        val rxPermissions = RxPermissions(this) // where this is an Activity instance
        rxPermissions.request(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CAMERA
        ).subscribe({ granted: Boolean ->
            if (granted) { // 在android 6.0之前会默认返回true
                selectPics()
            } else {
                // 未获取权限
                Toast.makeText(this@RegisterAddShopActivity, "您没有授权该权限，请在设置中打开授权", Toast.LENGTH_LONG)
                    .show()
            }
        }) { }
    }

    /**
     * 选择图片
     */
    private fun selectPics() {
        val mDialogLayout = ShowAptitudeBottomAddImageDialog(mySelf)
        mDialogLayout.setOnCancelClickListener { mDialogLayout.dismiss() }
        mDialogLayout.setOnPhotoGalleryClickListener {
            // 调用图库
            PictureSelector.create(mySelf)
                .openGallery(PictureMimeType.ofImage())
                .maxSelectNum(1)
                .minSelectNum(1)
                .imageSpanCount(4)
                .compress(true)
                .selectionMode(PictureConfig.MULTIPLE)
                .forResult(PictureConfig.CHOOSE_REQUEST)
            mDialogLayout.dismiss()
        }
        mDialogLayout.setOnTakingPicturesClickListener {

            //调用系统相机程序
            PictureSelector.create(mySelf)
                .openCamera(PictureMimeType.ofImage())
                .maxSelectNum(1)
                .minSelectNum(1)
                .compress(true)
                .forResult(PictureConfig.CHOOSE_REQUEST)
            mDialogLayout.dismiss()
        }
        mDialogLayout.show()
    }

    /**
     * 展示资质类型选择
     */
    private fun showAptitudeDialog() { //进入资质变更，也要弹出客户类型选择项
        if (customerTypeList.isNotEmpty()) {
            showCustomerTypeDialog(customerTypeList)
        } else {
            getCustomerType()
        }
    }

    private fun checkShopName() {
        val registerShop = register_shop.text.toString().trim { it <= ' ' }
        val registerProvince = mCurrentProviceName
        val registerCity = mCurrentCityName
        val registerDistrict = mCurrentDistrictName
        val registerStreet = mCurrentStreetName
        val registerAddressDetail = register_et_address.text.toString().trim { it <= ' ' }

        if (TextUtils.isEmpty(registerShop)) {
            ToastUtils.showShort("请输入店名")
            return
        }
        if (!UiUtils.isEnOrCNStrOrNumber(registerShop)) {
            ToastUtils.showShort("店名请使用汉字哦")
            return
        }
        if (registerShop.length < 5) {
            ToastUtils.showShort("店名不能少于5个字哦")
            return
        }

        if (TextUtils.isEmpty(registerProvince)) {
            ToastUtils.showShort("请选择地址")
            return
        }
        if (TextUtils.isEmpty(registerAddressDetail)) {
            ToastUtils.showShort("请输入详细地址")
            return
        }

        if (etCompanyType.text.toString().isEmpty()) {
            ToastUtils.showShort("请选择企业类型")
            return;
        }

        if (etNumber.text.toString().isEmpty()) {
            ToastUtils.showShort(if (codeType) "请输入营业执照编码" else "请输入医疗机构执业许可证编码")
            return
        }

        if (imageUrl?.isEmpty() == true) {
            ToastUtils.showShort(if (codeType) "请上传营业执照照片" else "请上传医疗机构执业许可证照片")
            return
        }

        showProgress()
        register_btn_ensure.isEnabled = false

        val params = RequestParams()
        params.put("name", registerShop)
        params.put("poiId", "0")
        params.put("address", registerAddressDetail)
        if (!TextUtils.isEmpty(registerCity)) {
            params.put("city", registerCity)
        }
        if (!TextUtils.isEmpty(registerProvince)) {
            params.put("province", registerProvince)
        }
        if (!TextUtils.isEmpty(registerDistrict)) {
            params.put("district", registerDistrict)
        }
        if (!TextUtils.isEmpty(registerStreet)) {
            params.put("street", registerStreet)
        }
        if (mCurrentProvinceId.isNotEmpty()) {
            params.put("provinceCode", "" + mCurrentProvinceId)
        }
        if (mCurrentCityId.isNotEmpty()) {
            params.put("cityCode", "" + mCurrentCityId)
        }
        if (mCurrentDistrictId.isNotEmpty()) {
            params.put("areaCode", "" + mCurrentDistrictId)
        }
        if (mCurrentStreetId.isNotEmpty()) {
            params.put("streetCode", "" + mCurrentStreetId)
        }
        params.put("customerType", "$mCustomerTypeId")
        params.put("licenseNo", etNumber.text.toString())
        params.put("licenseImageUrl", imageUrl)

        HttpManager.getInstance().post(AppNetConfig.MERCHANT_ADD_SHOP, params, object : BaseResponse<RegisterAddShopBean>() {

            override fun onSuccess(content: String?, obj: BaseBean<RegisterAddShopBean>?, data: RegisterAddShopBean?) {
                register_btn_ensure.isEnabled = true
                dismissProgress()
                if (null != obj) {
                    if (obj.isSuccess) {
                        if (data?.merchantId != null) {
//                            SpUtil.setMerchantid(data.merchantId)
                            val routerUrl = if (data.otherMerchantCount == 0) {
                                "ybmpage://shopinfoauthenticationprocessing?status=1&merchantId=${data.merchantId}"
                            } else {
                                "ybmpage://shopinfoauthenticationprocessing?status=2&merchantId=${data.merchantId}"
                            }
                            RoutersUtils.open(routerUrl)
                        }
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                register_btn_ensure.isEnabled = true
                dismissProgress()
            }
        })
    }

    private var addressListener: AddressProvider = object : AddressProvider {
        override fun provideProvinces(addressReceiver: AddressProvider.AddressReceiver<Province>) {
            val params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "0").build()
            HttpManager.getInstance().post(params, object : BaseResponse<List<Province>>() {
                override fun onSuccess(content: String?, bean: BaseBean<List<Province>>?, data: List<Province>?) {
                    if (bean == null || !bean.isSuccess) {
                        return
                    }
                    val provinces = bean.data
                    addressReceiver.send(provinces)
                }
            })
        }

        override fun provideCitiesWith(provinceId: String, addressReceiver: AddressProvider.AddressReceiver<Province>) {
            val params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + provinceId).build()
            HttpManager.getInstance().post(params, object : BaseResponse<List<Province>>() {
                override fun onSuccess(content: String?, bean: BaseBean<List<Province>>?, data: List<Province>?) {
                    if (bean == null || !bean.isSuccess) {
                        return
                    }
                    val provinces = bean.data
                    addressReceiver.send(provinces)
                }
            })
        }

        override fun provideCountiesWith(cityId: String, addressReceiver: AddressProvider.AddressReceiver<Province>) {
            val params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + cityId).build()
            HttpManager.getInstance().post(params, object : BaseResponse<List<Province>>() {
                override fun onSuccess(content: String?, bean: BaseBean<List<Province>>?, data: List<Province>?) {
                    if (bean == null || !bean.isSuccess) {
                        return
                    }
                    val provinces = bean.data
                    addressReceiver.send(provinces)
                }
            })
        }

        override fun provideStreetsWith(countyId: String, addressReceiver: AddressProvider.AddressReceiver<Province>) {
            // 2019/11/14 优化省市区街道
            val params = RequestParams.newBuilder().url(AppNetConfig.FIND_AREA_NEW)
                    .addParam("parentId", "" + countyId).build()
            HttpManager.getInstance().post(params, object : BaseResponse<List<Province>>() {
                override fun onSuccess(content: String?, bean: BaseBean<List<Province>>?, data: List<Province>?) {
                    if (bean == null || !bean.isSuccess) {
                        return
                    }
                    val provinces = bean.data
                    addressReceiver.send(provinces)
                }
            })
        }
    }

    override fun onAddressSelected(province: Province?, city: Province?, county: Province?, street: Province?) {
        if (dialog != null) {
            try {
                dialog!!.dismiss()
            } catch (e: Exception) {
            }

        }
        if (province == null || city == null) {
            return
        }

        val address = province.areaName + city.areaName + if (county == null) "" else county.areaName + if (street == null) "" else street.areaName
        register_tv_address_title.setText(address)

        mCurrentProviceName = province.areaName
        mCurrentCityName = city.areaName
        mCurrentDistrictName = if (county == null) "" else county.areaName
        mCurrentStreetName = if (county == null) "" else if (street == null) "" else street.areaName

        mCurrentProvinceId = province.areaCode
        mCurrentCityId = city.areaCode
        mCurrentDistrictId = county?.areaCode ?: ""
        mCurrentStreetId = if (county == null) "" else street?.areaCode ?: ""
    }

    private fun getCustomerType() { //获取客户类型
        showProgress()
        val params = RequestParams()
        HttpManager.getInstance().post(
            AppNetConfig.GET_CUSTOMER_TYPE_ALL,
            params,
            object: BaseResponse<List<CustomerTypeBean>>(){
                override fun onSuccess(
                    content: String?,
                    baseBean: BaseBean<List<CustomerTypeBean>>,
                    data: List<CustomerTypeBean>
                ) {
                    super.onSuccess(content, baseBean, data)
                    dismissProgress()
                    if (baseBean.isSuccess) {
                        if (data.isNotEmpty()) {
                            customerTypeList.clear()
                            customerTypeList.addAll(data)
                            setCustomerTypeDefaultSelectedIndex(firstCustomerType)
                            showCustomerTypeDialog(customerTypeList)
                        } else {
                            ToastUtils.showShort("获取客户类型数据为空")
                        }
                    }
                }

                override fun onFailure(error: NetError?) {
                    super.onFailure(error)
                    //TODO test-测试代码
                    imageUrl = "11111"
                    dismissProgress()
                }
            })
    }

    //设置默认上次选择的客户类型下标
    private fun setCustomerTypeDefaultSelectedIndex(firstType: Int) {
        if (customerTypeList.size > 0) {
            for (i in customerTypeList.indices) {
                if (customerTypeList[i].id == firstType) {
                    mCustomerTypeIndex = i
                }
            }
        }
    }

    /**
     * 展示客户类型选择
     */
    private fun showCustomerTypeDialog(customerTypeList: List<CustomerTypeBean>) {
        val title = "企业类型"
        PickerManager.showSingleSelectPicker(
            mySelf,
            title,
            customerTypeList,
            mCustomerTypeIndex,
            { index, customerTypeBean ->
                if (firstCustomerType != 0 && customerTypeBean?.id != firstCustomerType) {
                    customerTypeBean?.let { showBackDialog(index, it) }
                } else {
                    customerTypeBean?.let { switchCode(index, it) }
                }
            },
            object : PickerManager.Adapter<CustomerTypeBean?>() {
                override fun getStr(customerTypeBean: CustomerTypeBean?): String = customerTypeBean?.name?: ""
            })
    }

    private fun showBackDialog(index: Int, customerTypeBean: CustomerTypeBean) {
        val dialogEx = AlertDialogEx(this)
        dialogEx.setTitle("")
            .setMessage("变更资质类型将覆盖原资质，\n是否替换？")
            .setCancelButton("取消",
                AlertDialogEx.OnClickListener { _, _ -> })
            .setConfirmButton("替换",
                AlertDialogEx.OnClickListener { _, _ ->
                    switchCode(index, customerTypeBean)
                })
            .show()
    }

    private fun switchCode(index: Int, customerTypeBean: CustomerTypeBean) {
        codeType = customerTypeBean.remark
        mCustomerTypeIndex = index
        mCustomerTypeId = customerTypeBean.id
        etCompanyType.setText(customerTypeBean.name)
        handleCodeMaxLengthAndHint()
        etNumber.setText("")
        switchImage(false, null)
    }

    /**
     * 设置编码最大长度
     */
    private fun handleCodeMaxLengthAndHint() {
        if (codeType) {
            //true: 营业执照编码
            etNumber.filters = arrayOf<InputFilter>(LengthFilter(18))
            etNumber.hint = ""
            ilNumber.hint = "请填写营业执照编码"
            tvImageTitle.text = "请上传营业执照电子版"
        } else {
            //false:医疗机构执业许可证编码
            etNumber.filters = arrayOf<InputFilter>(LengthFilter(23))
            etNumber.hint = ""
            ilNumber.hint = "请填写医疗机构执业许可证编码"
            tvImageTitle.text = "请上传医疗机构执业许可证电子版"
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == PictureConfig.CHOOSE_REQUEST) {
            val images = PictureSelector.obtainMultipleResult(data)
            if (images == null || images.isEmpty()) {
                ToastUtils.showShort("未找到图片")
                return
            }
            val selectList = ArrayList<LocalMedia>(3)
            selectList.addAll(images)
            val imageInfos = ArrayList<LicensePicListAdapter.ImageInfo>()
            for (localMedia in selectList) {
                val imageInfo = LicensePicListAdapter.ImageInfo()
                imageInfo.localPath = localMedia.compressPath
                imageInfos.add(imageInfo)
            }
            uploadImage(imageInfos)
        }
    }

    private fun uploadImage(imageInfoList: List<LicensePicListAdapter.ImageInfo>) {
        for (info in imageInfoList) {
            val path = info.localPath?: kotlin.run {
                ToastUtils.showShort("图片未找到：" + info.localPath, Toast.LENGTH_LONG)
                return
            }
            val file = File(path)
            if (!file.exists()) {
                ToastUtils.showShort("图片未找到：" + info.localPath, Toast.LENGTH_LONG)
                return
            }
        }
        showProgress(false)
        val linkedList = LinkedList(imageInfoList)
        uploadCircle(linkedList)
    }

    private fun uploadCircle(linkedList: LinkedList<LicensePicListAdapter.ImageInfo>) {
        if (linkedList.isEmpty()) return
        val s1 = linkedList[0]
        val file = File(s1.localPath)
        if (!file.exists()) {
            ToastUtils.showShort("上传文件不存在")
            return
        }
        val params = RequestParams()
        params.put("file", file)
        HttpManager.getInstance().post(
            AppNetConfig.ADD_SHOP_UPLOAD_IMAGE,
            params,
            object: BaseResponse<List<String>>(){
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<List<String>>?,
                    bean: List<String>?
                ) {
                    super.onSuccess(content, obj, bean)
                    dismissProgress()
                    if (bean?.isNotEmpty() == true) {
                        //拼上上传的图片全路径 /ybm/license/**********/334dd569-5716-45d4-999d-46833ac9633c.jpeg
                        imageUrl = bean[0]
                        switchImage(true, file.absolutePath)
                    } else {
                        ToastUtils.showShort("上传失败")
                    }
                }

                override fun onFailure(error: NetError?) {
                    super.onFailure(error)
                    dismissProgress()
                }
            }
        )
    }

    /**
     * 切换图片
     */
    private fun switchImage(isSelected: Boolean, url: String?) {
        clImageUpdate.visibility = if (isSelected) View.VISIBLE else View.GONE
        clAddImageBtn.visibility = if (isSelected) View.GONE else View.VISIBLE
        if (isSelected) {
            url?.let { ivUpdate.setImageBitmap(BitmapFactory.decodeFile(it)) }
        } else {
            imageUrl = null
        }
        register_btn_ensure.checkButtonStatus()
    }

    override fun onBackPressed() {
        finish()
    }
}
