package com.ybmmarket20.activity;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.github.mzule.activityrouter.annotation.Router;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AptitudeLicenseBean;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.UpLoadLicenseBean;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.BitmapUtil;
import com.ybmmarket20.utils.GalleryUtil;
import com.ybmmarket20.utils.SpUtil;

import java.io.File;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * 资质上传
 */
@Router("dataupdateactivity")
public class DataUpdateActivity extends BaseActivity {

    private static final int CAMERA = 100;
    private static final int PICTURE = 200;
    @Bind(R.id.dataupdate_photo)
    RelativeLayout dataupdatePhoto;
    @Bind(R.id.dataupdate_shoot)
    RelativeLayout dataupdateShoot;
    @Bind(R.id.dataupdate_iv)
    ImageView dataupdateIv;
    @Bind(R.id.dataupdate_submit_btn)
    Button dataupdateSubmitBtn;
    @Bind(R.id.dataupdate_btn)
    Button dataupdateBtn;
    @Bind(R.id.dataupdate_tv_name)
    TextView nameTv;

    private String path;
    private AptitudeLicenseBean listBean;
    private int position;
    private File file;
    private String PHOTO_DIR;// 存储照片的位置
    private File cameraFile;

    @Override
    protected void initData() {
        setTitle("资料上传");
        dataupdateBtn.setVisibility(View.GONE);
        listBean = (AptitudeLicenseBean) getIntent().getParcelableExtra(IntentCanst.APTITUDE_UPDATE);
        position = getIntent().getIntExtra(IntentCanst.APTITUDE_POSITION, -1);
        nameTv.setText(listBean.name);
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_dataupdate;
    }

    @OnClick({R.id.dataupdate_photo, R.id.dataupdate_shoot, R.id.dataupdate_btn, R.id.dataupdate_submit_btn})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.dataupdate_photo: //相册
                //调用系统打开外部图库程序
                Intent picture = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                picture.setType("image/*");
                startActivityForResult(picture, PICTURE);
                break;
            case R.id.dataupdate_shoot: //相机
                //调用系统相机程序
                Intent camera = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                cameraFile = getPhotoFile();
                camera.putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(cameraFile));
                startActivityForResult(camera, CAMERA);
                break;
            case R.id.dataupdate_btn:
            case R.id.dataupdate_submit_btn:
                dataupdateSubmitBtn.setEnabled(false);
                try {
                    uploadFile(path, AppNetConfig.PICTURE_CALL);
                } catch (Exception e) {
                    e.printStackTrace();
                    ToastUtils.showShort("图片上传失败");
                    dataupdateSubmitBtn.setEnabled(true);
                }
                break;
        }
    }

    //提交审核
    private void submitAptitude() {
        final int categoryId = listBean.categoryId;
        final String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        params.put("categoryId", String.valueOf(categoryId));
        params.put("name", listBean.name);
        params.put("url", listBean.name+ ".png");
        params.put("status", String.valueOf(1));
        if (listBean.id > 0) {
            params.put("id", String.valueOf(listBean.id));
        }
        String url = listBean.id > 0 ? AppNetConfig.UPDATE_APTITUDE : AppNetConfig.SAVE_APTITUDE;
        HttpManager.getInstance().post(url, params, new BaseResponse<UpLoadLicenseBean>() {

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
                dataupdateSubmitBtn.setEnabled(true);
            }

            @Override
            public void onSuccess(String content, BaseBean<UpLoadLicenseBean> result,UpLoadLicenseBean data) {
                dismissProgress();
                if (result != null && result.isSuccess() && data !=null) {
                    Intent intent = new Intent();
                    intent.putExtra(IntentCanst.APTITUDE_POSITION, position);
                    Bundle mBundle = new Bundle();
                    listBean.setUrl(listBean.name + ".png");
                    listBean.setStatus(1);
                    if (listBean.id <= 0) {
                        listBean.setId(data.id);
                    }
                    listBean.setMerchantId(merchantid);
                    listBean.setCategoryId(categoryId);
                    mBundle.putParcelable(IntentCanst.APTITUDE_UPDATE, listBean);
                    intent.putExtras(mBundle);
                    setResult(Activity.RESULT_OK, intent);
                    finish();
                } else {
                    ToastUtils.showShort("上传资质失败，请重试");
                    dataupdateSubmitBtn.setEnabled(true);
                }
            }
        });
    }

    /**
     * @param path 要上传的文件路径
     * @param url  服务端接收URL
     * @throws Exception
     */
    public void uploadFile(String path, String url) throws Exception {
        if (TextUtils.isEmpty(path)) {
            ToastUtils.showShort("请选择图片在上传");
            dataupdateSubmitBtn.setEnabled(true);
        }
        String merchantid = SpUtil.getMerchantid();
        file = new File(path);
        if (file.exists() && file.length() > 0) {
            RequestParams params = new RequestParams();
            params.put("uploadPath", "ybm/license/" + merchantid + "/");
            params.put("targetFileName", listBean.name);
            params.put("file", file);
            showProgress("图片上传中...", true, false);
            HttpManager.getInstance().post(url, params, new BaseResponse<EmptyBean>() {
                @Override
                public void onSuccess(String content, BaseBean<EmptyBean> obj,EmptyBean data) {
                    if (obj != null && obj.isSuccess()) {
                        LogUtils.d("上传完成，开始更新");
                        submitAptitude();
                    } else {
                        ToastUtils.showShort("上传失败");
                        dismissProgress();
                        dataupdateSubmitBtn.setEnabled(true);
                    }
                }


                @Override
                public void onFailure(NetError error) {
                    ToastUtils.showShort("上传失败");
                    dismissProgress();
                    dataupdateSubmitBtn.setEnabled(true);
                }

            });

        } else {
            ToastUtils.showShort("文件不存在");
            dataupdateSubmitBtn.setEnabled(true);
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_CANCELED) {
            return;
        }
        path = getCacheDir().getAbsolutePath() + "/tx.png";
        if (requestCode == CAMERA) {
            //来自于相机返回的结果
            File file = null;
            if (data == null) {//设置的图片存储的uri
                file = cameraFile;
                if (file != null && file.exists()) {
                    if (BitmapUtil.compressFile(file.getAbsolutePath(), path)) {
                        file = new File(path);
                    }
                }
            } else {//没有设置图片的uri
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    Bitmap bitmap = (Bitmap) bundle.get("data");
                    file = BitmapUtil.bitmapToFile(bitmap, path);
                } else {//android 5.0 系统
                    if (data.getData() != null && !TextUtils.isEmpty(data.getData().getEncodedPath())) {
                        if (BitmapUtil.compressFile(data.getData().getEncodedPath(), path)) {
                            file = new File(path);
                        }
                    }
                }
            }
            if (file == null || !file.exists()) {
                path = null;
                dataupdateIv.setImageBitmap(null);
                ToastUtils.showShort("没有找到图片");
            } else {
                ImageHelper.with(this).load(Uri.fromFile(file)).skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE).into(dataupdateIv);
            }
        } else if (requestCode == 200 && resultCode == RESULT_OK && data != null) {
            //来自于图库返回的结果
            Uri selectedImage = data.getData();
            String pathResult = GalleryUtil.getFilePathByUri(DataUpdateActivity.this, selectedImage);
            if (TextUtils.isEmpty(pathResult) || !BitmapUtil.compressFile(pathResult, path)) {
                path = null;
                dataupdateIv.setImageBitmap(null);
                ToastUtils.showShort("没有找到图片");
            } else {
                Log.i("xuejian", "path = " + path);
                ImageHelper.with(this).load(Uri.fromFile(new File(path))).skipMemoryCache(true).diskCacheStrategy(DiskCacheStrategy.NONE).into(dataupdateIv);
            }
        }
    }

    //生成相机来的图片
    public File getPhotoFile() {
        if (TextUtils.isEmpty(PHOTO_DIR)) {
            createImgFolders();
        }
        File file = new File(PHOTO_DIR, "xyy.jpg");
        if (file.exists()) {
            try {
                file.delete();
                file.createNewFile();
            } catch (Exception e) {
                LogUtils.d(e);
            }
        }
        LogUtils.d(file.getAbsolutePath());
        return file;
    }

    private void createImgFolders() {
        if (!Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())) {
            // 如果未加载SD卡，存放在内置卡中
            PHOTO_DIR = getCacheDir().getAbsolutePath()
                    + File.separator + "pics" + File.separator;
        } else {
            PHOTO_DIR = Environment.getExternalStorageDirectory()
                    + "/DCIM/Camera";
        }
        File photoDir = new File(PHOTO_DIR);
        if (!photoDir.exists()) {
            photoDir.mkdirs();
            photoDir = null;
        }
    }
}