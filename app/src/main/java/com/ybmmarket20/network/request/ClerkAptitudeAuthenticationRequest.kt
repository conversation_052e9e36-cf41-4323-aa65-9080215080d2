package com.ybmmarket20.network.request

import com.ybmmarket20.bean.AptitudeAuthenticationLayoutData
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * 资质认证
 */
interface IClerkAptitudeAuthenticationRequest {
    @FormUrlEncoded
    @POST("uploadAuthorization")
    suspend fun confirmClerkAptitudeAuthentication(@Field("licenseAuditImgListStr")licenseAuditImgListStr: String, @Field("merchantId")merchantId: String): BaseBean<EmptyBean>

    @FormUrlEncoded
    @POST("getUploadAuthorization")
    suspend fun getAptitudeAuthenticationLayoutData(@Field("merchantId")merchantId: String): BaseBean<AptitudeAuthenticationLayoutData>
}

class ClerkAptitudeAuthenticationRequest {

    /**
     * 店员提交委托书
     */
    suspend fun confirmClerkAptitudeAuthentication(licenseAuditImgListStr: String, merchantId: String): BaseBean<EmptyBean> = try {
        NetworkService.instance.mRetrofit.create(IClerkAptitudeAuthenticationRequest::class.java).confirmClerkAptitudeAuthentication(licenseAuditImgListStr, merchantId)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }

    /**
     * 获取店员提交凭据布局数据
     */
    suspend fun getAptitudeAuthenticationLayoutData(merchantId: String): BaseBean<AptitudeAuthenticationLayoutData> = try {
        NetworkService.instance.mRetrofit.create(IClerkAptitudeAuthenticationRequest::class.java).getAptitudeAuthenticationLayoutData(merchantId)
    } catch (e: Exception) {
        BaseBean<AptitudeAuthenticationLayoutData>().initWithException(e)
    }

}