package com.ybmmarket20.utils;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import androidx.core.content.FileProvider;
import android.text.TextUtils;

import com.alipay.share.sdk.openapi.*;
import com.bumptech.glide.GenericRequestBuilder;
import com.bumptech.glide.Glide;
import com.bumptech.glide.RequestManager;
import com.luck.picture.lib.compress.Luban;
import com.tencent.connect.share.QQShare;
import com.tencent.mm.opensdk.modelmsg.*;
import com.tencent.tauth.IUiListener;
import com.tencent.tauth.Tencent;
import com.tencent.tauth.UiError;
import com.tencent.wework.api.WWAPIFactory;
import com.tencent.wework.api.model.WWMediaImage;
import com.tencent.wework.api.model.WWMediaLink;
import com.ybm.app.common.SmartExecutorManager;
import com.ybmmarket20.R;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.util.Abase;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.ConstantData;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;

/**
 * Created by wh on 2018/4/25.
 */

public class ShareUtil {

    // 支付宝APPID
    public static String SHARE_ALI_APPID = "2021002102614273";
    //企微debug
//    public static String SHARE_WEWORK_SCHEMA = "wwauth97630ccc40bc5795000054";
//    public static String SHARE_WEWORK_AGENT_ID = "1000054";
//    public static String SHARE_WEWORK_APP_ID = "ww97630ccc40bc5795";
    //企微release
    public static String SHARE_WEWORK_SCHEMA = "wwauth97630ccc40bc5795000055";
    public static String SHARE_WEWORK_AGENT_ID = "1000055";
    public static String SHARE_WEWORK_APP_ID = "ww97630ccc40bc5795";

    private static YBMWxUtil.SDKCallBack mCallBack;

    public static void setPaySDKCallBack(YBMWxUtil.SDKCallBack payCall) {
        mCallBack = payCall;
    }
    //小程序分享链接
    public static void shareWXMiniProg(String title, String url, String path, String id, String desc, Bitmap bmp, boolean isCompress) {
        if (YBMAppLike.mApi == null) {
            return;
        }
        WXMiniProgramObject page = new WXMiniProgramObject();
        page.path = path;
        page.miniprogramType = WXMiniProgramObject.MINIPTOGRAM_TYPE_RELEASE;
        page.userName = id;
        page.webpageUrl = url;

        WXMediaMessage msg = new WXMediaMessage(page);
        msg.title = title;
        msg.description = desc;
        byte[] thumb;
        if (isCompress) {
            thumb = bitmap2byte(bmp);
        } else {
            thumb = bitmap2byteNoCompress(bmp);
        }
        if (thumb != null) {
            msg.thumbData = thumb;
        }

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.message = msg;
        req.transaction = System.currentTimeMillis() + "";
        req.scene = SendMessageToWX.Req.WXSceneSession;
        YBMAppLike.mApi.sendReq(req);
    }

    /**
     * 微信分享连接
     */
    public static void shareWXPage(String title, String url, String desc, Bitmap bmp) {
        if (YBMAppLike.mApi == null) {
            return;
        }
        WXWebpageObject page = new WXWebpageObject();
        page.webpageUrl = url;

        WXMediaMessage msg = new WXMediaMessage(page);
        msg.title = title;
        msg.description = desc;
        byte[] thumb = bitmap2byte(bmp);
        if (thumb != null) {
            msg.thumbData = thumb;
        }

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.message = msg;
        req.transaction = System.currentTimeMillis() + "";
        req.scene = SendMessageToWX.Req.WXSceneSession;
        YBMAppLike.mApi.sendReq(req);
    }

    /**
     * 分享支付宝好友 Url
     * @param applicationContext
     * @param title
     * @param url
     * @param desc
     * @param imgUrl
     */
    public static void shareAliPayPage(Context applicationContext, String title, String url, String desc, String imgUrl) {
        shareAliPayPageBase(applicationContext, title, url, desc, imgUrl, null, false);
    }

    /**
     * 分享支付宝好友 Bitmap
     * @param applicationContext
     * @param title
     * @param url
     * @param desc
     * @param bitmap
     */
    public static void shareAliPayPage(Context applicationContext, String title, String url, String desc, Bitmap bitmap) {
        shareAliPayPageBase(applicationContext, title, url, desc, null, bitmap, true);
    }

    /**
     * 分享支付宝好友
     * @param applicationContext (context.getApplicationContext())
     * @param title 标题
     * @param url 网页链接
     * @param desc 描述
     * @param imgUrl 图片链接
     * @param bitmap 图片bitmap
     * @param isBitmap 是否是分享bitmap
     */
    public static void shareAliPayPageBase(Context applicationContext, String title, String url, String desc, String imgUrl, Bitmap bitmap, boolean isBitmap) {
        IAPApi api = APAPIFactory.createZFBApi(applicationContext, ShareUtil.SHARE_ALI_APPID, false);
        boolean isZFBInstalled = api.isZFBAppInstalled();
        boolean isZFBSupportLife = api.isZFBSupportAPI();
        if (!isZFBInstalled) {
            ToastUtils.showShort("请安装支付宝");
            return;
        }
        if (!isZFBSupportLife) {
            ToastUtils.showShort("当前支付宝版本不支持分享到好友，请现在最新版本");
            return ;
        }
        //初始化一个APWebPageObject对象，组装网页Card内容对象
        APWebPageObject webPageObject = new APWebPageObject();
        webPageObject.webpageUrl = url;
        //初始化APMediaMessage ，组装分享消息对象
        APMediaMessage webMessage = new APMediaMessage();
        webMessage.mediaObject = webPageObject;
        webMessage.title = title;
        webMessage.description = desc;
        //网页缩略图的分享支持bitmap和url两种方式，直接通过bitmap传递时bitmap最大为32K
        //a）url方式
        //webMessage.thumbUrl = "http://www.yoururl.com/thumb.jpg";
        //b）Bitmap方式
        //webMessage.setThumbImage(bitmap);
        //bitmap.recycle();
        if (isBitmap) {
            webMessage.setThumbImage(bitmap);
        } else {
            webMessage.thumbUrl = imgUrl;
        }
        //将分享消息对象包装成请求对象
        SendMessageToZFB.Req webReq = new SendMessageToZFB.Req();
        webReq.message = webMessage;
        webReq.transaction = "WebShare"+String.valueOf(System.currentTimeMillis());
        //发送请求
        api.sendReq(webReq);
    }

    private static String buildTransaction(final String type) {
        return (type == null) ? String.valueOf(System.currentTimeMillis()) : type + System.currentTimeMillis();
    }

    private static boolean isAlipayIgnoreChannel(IAPApi api) {
        return api.getZFBVersionCode() >= 101;
    }

    /**
     * 微信分享连接
     * 0       分享到微信好友
     * other   分享到朋友圈
     * flag=0 WXSceneSession分享微信朋友 flag=1 WXSceneTimeline分享朋友圈
     */
    public static void shareWXPage(int flag, String title, String url, String desc, Bitmap bmp) {
        if (YBMAppLike.mApi == null) {
            return;
        }
        WXWebpageObject page = new WXWebpageObject();
        page.webpageUrl = url;

        WXMediaMessage msg = new WXMediaMessage(page);
        msg.title = title;
        msg.description = desc;
        byte[] thumb = bitmap2byte(bmp);
        if (thumb != null) {
            msg.thumbData = thumb;
        }

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.message = msg;
        req.transaction = System.currentTimeMillis() + "";
        req.scene = SendMessageToWX.Req.WXSceneSession;
        req.scene = flag == 0 ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
        YBMAppLike.mApi.sendReq(req);

        if (mCallBack != null) {
            mCallBack.sdkCallBackTwo();
        }
    }

    /**
     * 微信分享文本
     */
    public static void shareWXWithText(String title, String content, String desc) {
        if (YBMAppLike.mApi == null) {
            return;
        }
        WXTextObject object = new WXTextObject();
        object.text = content;

        WXMediaMessage message = new WXMediaMessage();
        message.mediaObject = object;
        message.title = title;
        message.description = desc;

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.message = message;
        req.transaction = System.currentTimeMillis() + "";
        req.scene = SendMessageToWX.Req.WXSceneSession;
        YBMAppLike.mApi.sendReq(req);
    }

    /**
     * 微信分享图片
     */
    public static void shareWXWithImage(int flag, Bitmap bmp, int with, int height) {
        if (YBMAppLike.mApi == null) {
            return;
        }
        WXImageObject object = new WXImageObject(bmp);
        WXMediaMessage message = new WXMediaMessage();
        message.mediaObject = object;
        //缩略图
        message.thumbData = bitmap2byte(bmp);
        bmp.recycle();

        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.message = message;
        req.scene = flag == 0 ? SendMessageToWX.Req.WXSceneSession : SendMessageToWX.Req.WXSceneTimeline;
        YBMAppLike.mApi.sendReq(req);

    }

    /**
     * 企业微信分享图片
     */
    public static void shareWeworkWithImage(int flag, Bitmap bmp, int with, int height) {
        if (YBMAppLike.mWWApi == null) {
            return;
        }
        if (!isWeworkInstall()) return;
        byte[] i = bitmap2byteNoCompress(bmp);
        WWMediaImage img = new WWMediaImage(i);
        img.fileName = System.currentTimeMillis()+"";
        img.appPkg = Abase.getContext().getPackageName();
        img.appName = "药帮忙";
        img.appId = SHARE_WEWORK_APP_ID;
        img.agentId = SHARE_WEWORK_AGENT_ID;
        YBMAppLike.mWWApi.sendMessage(img);
    }


    /**
     * 企业微信分享连接
     */
    public static void shareWeworkPage(int flag, String title, String url, String desc, Bitmap bmp) {
        if (YBMAppLike.mWWApi == null) {
            return;
        }
        if (!isWeworkInstall()) return;
        WWMediaLink link = new WWMediaLink();
        // 降级方案：先缩小采样率再缩放
        // 降级方案：先缩小采样率再缩放
        byte[] thumb = bitmap2byte(bmp);
        if (thumb != null) {
            link.thumbData = thumb;
        }
        link.webpageUrl = url;
        link.title = title;
        link.description = desc;
        link.appPkg = Abase.getContext().getPackageName();
        link.appName = "药帮忙";
        link.appId = SHARE_WEWORK_APP_ID; //企业唯一标识。创建企业后显示在，我的企业 CorpID字段
        link.agentId = SHARE_WEWORK_AGENT_ID; //应用唯一标识。显示在具体应用下的 AgentId字段
        YBMAppLike.mWWApi.sendMessage(link);
    }

    /**
     * QQ常规分享
     */
    public static void shareQQCommon(Activity activity, String title, String url, String image, String desc, Tencent tencent, IUiListener listener) {
        if (activity == null || activity.isFinishing() || tencent == null) {
            return;
        }
        final Bundle params = new Bundle();
        params.putInt(QQShare.SHARE_TO_QQ_KEY_TYPE, QQShare.SHARE_TO_QQ_TYPE_DEFAULT);
        params.putString(QQShare.SHARE_TO_QQ_TITLE, title);
        params.putString(QQShare.SHARE_TO_QQ_SUMMARY, desc == null ? "" : desc);
        params.putString(QQShare.SHARE_TO_QQ_TARGET_URL, url);
        params.putString(QQShare.SHARE_TO_QQ_IMAGE_URL, image);
        params.putInt(QQShare.SHARE_TO_QQ_EXT_INT, QQShare.SHARE_TO_QQ_FLAG_QZONE_ITEM_HIDE);
        SmartExecutorManager.getInstance().executeUI(new Runnable() {
            @Override
            public void run() {
                tencent.shareToQQ(activity, params, listener);
            }
        });
    }

    /**
     * Bitmap转byte数组
     */
    public static byte[] bitmap2byte(Bitmap bmp) {
        if (bmp == null) {
            return null;
        }
        try {
            Bitmap thum = Bitmap.createScaledBitmap(bmp, 100, 100, true);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            thum.compress(Bitmap.CompressFormat.PNG, 100, baos);
            baos.close();
            return baos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Bitmap转byte数组
     */
    public static byte[] bitmap2byteNoCompress(Bitmap bmp) {
        if (bmp == null) {
            return null;
        }
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            bmp.compress(Bitmap.CompressFormat.PNG, 100, baos);
            baos.close();
            return baos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean isWeChatInstall() {
        PackageManager manager = YBMAppLike.getAppContext().getPackageManager();
        if (YBMAppLike.mApi.isWXAppInstalled()) return true;
        if (manager == null || manager.getInstalledPackages(0) == null) {
            ToastUtils.showShort("微信未安装");
            return false;
        }
        List<PackageInfo> infoList = manager.getInstalledPackages(0);
        for (PackageInfo info :
                infoList) {
            if ("com.tencent.mm".equals(info.packageName)) {
                return true;
            }
        }
        ToastUtils.showShort("微信未安装");
        return false;
    }

    public static boolean isQQInstall() {
        PackageManager manager = YBMAppLike.getAppContext().getPackageManager();
        if (manager == null || manager.getInstalledPackages(0) == null) {
            ToastUtils.showShort("QQ未安装");
            return false;
        }
        List<PackageInfo> infoList = manager.getInstalledPackages(0);
        for (PackageInfo info :
                infoList) {
            if ("com.tencent.mobileqq".equals(info.packageName)) {
                return true;
            }
        }
        ToastUtils.showShort("QQ未安装");
        return false;
    }

    public static boolean isWeChatOrQQInstall(String platform) {
        if ("wx".equals(platform)) {
            return isWeChatInstall();
        } else if ("wxpyq".equals(platform)) {
            return isWeChatInstall();
        } else if ("bctp".equals(platform)) {
            return true;
        } else {
            return isQQInstall();
        }
    }

    /**
     * 判断企业微信是否安装
     * @return
     */
    public static boolean isWeworkInstall() {
        PackageManager manager = YBMAppLike.getAppContext().getPackageManager();
        if (YBMAppLike.mWWApi.isWWAppInstalled()) return true;
        if (manager == null || manager.getInstalledPackages(0) == null) {
            return false;
        }
        List<PackageInfo> infoList = manager.getInstalledPackages(0);
        for (PackageInfo info :
                infoList) {
            if ("com.tencent.wework".equals(info.packageName)) {
                return true;
            }
        }
        return false;
    }


    public static void share(Activity activity, String url, String title, String desc, String photo) {
        DialogUtil.shareDialog(activity, new DialogUtil.DialogSingleClick() {
            @Override
            public void click(String platform) {
                if (TextUtils.isEmpty(platform)) {
                    return;
                }
                if (!ShareUtil.isWeChatOrQQInstall(platform)) {
                    return;
                }
                if ("wx".equals(platform)) {
                    Bitmap bmp = BitmapFactory.decodeResource(activity.getResources(), R.drawable.logo);
                    ShareUtil.shareWXPage(title, url, desc, bmp);
                } else if ("qq".equals(platform)) {
                    Tencent tencent = Tencent.createInstance(ConstantData.APP_ID_QQ, activity);
                    ShareUtil.shareQQCommon(activity, title, url, photo, desc, tencent, new IUiListener() {
                        @Override
                        public void onComplete(Object o) {
                            ToastUtils.showShort("分享成功");
                        }

                        @Override
                        public void onError(UiError uiError) {

                        }

                        @Override
                        public void onCancel() {
                            ToastUtils.showShort("取消分享");
                        }

                        @Override
                        public void onWarning(int i) {

                        }
                    });
                }
            }
        });
    }
    /**
     * 通过系统分享pdf文件到微信好友
     */
    public static void sharePdfFileWechatFriend(Context mContext, File file) {
         Uri uri;
        int currentapiVersion = android.os.Build.VERSION.SDK_INT;
        if (currentapiVersion >= Build.VERSION_CODES.N) {//若SDK大于等于24  获取uri采用共享文件模式
            uri = FileProvider.getUriForFile(mContext, mContext.getPackageName() + ".fileProvider", file);
        } else {
            uri = Uri.fromFile(file);
        }
        Intent share = new Intent(Intent.ACTION_SEND);
        share.putExtra(Intent.EXTRA_STREAM, uri);
        share.setType("application/pdf");//此处可发送多种文件
        share.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        share.addCategory(Intent.CATEGORY_DEFAULT);
        share.setPackage("com.tencent.mm");
        if (isWeChatInstall()){
            try  {
                mContext.startActivity(Intent.createChooser(share, "分享"));
            } catch (ActivityNotFoundException e) {
                ToastUtils.showShort("暂不支持微信分享");
                e.printStackTrace();
            }
        }else {
            ToastUtils.showShort("您需要安装微信客户端");
        }
    }

    /**
     * 通过系统分享url到微信好友
     */
    public static void shareUrlWechatFriend(Context mContext, String url) {
        Intent share = new Intent(Intent.ACTION_SEND);
        try {
            share.putExtra(Intent.EXTRA_STREAM, new URI(url));
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        share.setDataAndType(Uri.parse(url), "video/*");
        share.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        share.addCategory(Intent.CATEGORY_DEFAULT);
        share.setPackage("com.tencent.mm");
        if (isWeChatInstall()){
            mContext.startActivity(Intent.createChooser(share, "分享"));
        }else {
            ToastUtils.showShort("您需要安装微信客户端");
        }
    }

    /**
     * 通过系统分享pdf文件到微信好友
     */
    public static void shareVideoWechatFriend(BaseActivity context, String videoUrl) {
        context.showProgress();
        WXVideoObject video = new WXVideoObject();
        video.videoUrl = videoUrl;
        WXMediaMessage msg = new WXMediaMessage(video);
        msg.title = "药帮忙客服";
        msg.description = "";
        SmartExecutorManager.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                ByteArrayOutputStream baos = null;
                try {
//                    MediaMetadataRetriever media = new MediaMetadataRetriever();
//                    media.setDataSource(videoUrl, new HashMap());
//                    Bitmap bitmap = media.getFrameAtTime();
//                    media.release();
                    Bitmap bitmap = BitmapFactory.decodeResource(context.getResources(), R.drawable.logo);
                    baos = new ByteArrayOutputStream();
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 10, baos);
                    msg.thumbData = baos.toByteArray();
                    SendMessageToWX.Req req = new SendMessageToWX.Req();
                    req.message = msg;
                    req.transaction = buildTransaction("video");
                    req.scene = SendMessageToWX.Req.WXSceneSession;
                    YBMAppLike.mApi.sendReq(req);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (!context.isDestroy) context.dismissProgress();
                    if (baos != null) {
                        try {
                            baos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        });
    }
}
