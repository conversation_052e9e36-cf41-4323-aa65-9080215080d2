package com.ybmmarket20.utils

import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.db.AccountTable

/**
 * @author: yuhaibo
 * @time: 2019-08-26 16:41.
 * projectName: ybm-android.
 * Description:
 */
object CommonUtil {

    fun skipCertification() {
        val accountList = AccountTable.getAllAccount(Abase.getContext())
        val currentAccountBean = accountList?.find { SpUtil.getMerchantid() == it.merchantId }
        RoutersUtils.open("ybmpage://commonh5activity?url=" + AppNetConfig.ATTORNEY_INFO_CONFIRM + "merchantId=" + SpUtil.getMerchantid() + "&merchantName=" + currentAccountBean?.shopName)
    }
}