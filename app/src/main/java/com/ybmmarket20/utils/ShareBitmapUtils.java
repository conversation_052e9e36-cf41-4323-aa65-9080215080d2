package com.ybmmarket20.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.Pair;

import com.ybmmarket20.R;

/**
 * 生成分享图片
 */
public class ShareBitmapUtils {

    //右对齐位置
    public static final int ALIGN_END = 450;
    //bitmap宽度
    public static final int BITMAP_WIDTH = 500;
    //bitmap高度
    public static final int BITMAP_HEIGHT = 400;

    public Builder build;

    public ShareBitmapUtils(Builder build) {
        this.build = build;
    }

    public static class Builder {
        //店铺名称
        String shopName = "";
        //价格
        String price = "";
        //时间
        String time = "";
        //背景图
        Bitmap bgBitmap = null;
        Paint mPaint = null;

        public Builder(Bitmap bg) {
            if (bg == null) throw new IllegalArgumentException("Miss background bitmap");
            bgBitmap = bg;
            mPaint = new Paint();
            mPaint.setAntiAlias(true);
        }

        public Builder(int bgRes, Context context) {
            if (bgRes <= 0) throw new IllegalArgumentException("illegal params bgRes");
            Bitmap bgResourceBitmap = BitmapFactory.decodeResource(context.getResources(), bgRes);
            bgBitmap = bgResourceBitmap;
            mPaint = new Paint();
            mPaint.setAntiAlias(true);
        }

        /**
         * 设置店铺名称
         * @param shopName
         * @return
         */
        public Builder setShopName(String shopName) {
            if (shopName == null) throw new IllegalArgumentException("shopName = null");
            this.shopName = shopName;
            return this;
        }

        /**
         * 设置价格
         * @param price
         * @return
         */
        public Builder setPrice(String price) {
            if (price == null) throw new IllegalArgumentException("shopName = null");
            this.price = price;
            return this;
        }

        /**
         * 设置时间
         * @param time
         * @return
         */
        public Builder setTime(String time) {
            if (time == null) throw new IllegalArgumentException("shopName = null");
            this.time = time;
            return this;
        }

        public ShareBitmapUtils build() {
            return new ShareBitmapUtils(this);
        }
    }

    /**
     * 生成bitmap
     * @param context
     * @return
     */
    public Bitmap generateBitmap(Context context) {
        Bitmap bitmap = Bitmap.createBitmap(BITMAP_WIDTH, BITMAP_HEIGHT, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.drawBitmap(build.bgBitmap, new Rect(0, 0, build.bgBitmap.getWidth(), build.bgBitmap.getHeight()), new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight()), build.mPaint);
        drawShopName(build.mPaint, ALIGN_END, build.shopName, canvas);
        drawTime(build.mPaint, ALIGN_END, build.time, canvas);
        drawPrice(build.mPaint, ALIGN_END, build.price, canvas);
        return bitmap;
    }

    /**
     * 获取分享bitmap
     * @param context
     * @param shareData
     * @return 缺少指定参数直接返回null
     */
    public static Bitmap getShareBitmap(Context context, PayAnotherShareData shareData) {
        if (TextUtils.isEmpty(shareData.shopName) || TextUtils.isEmpty(shareData.createTimeDesc) || TextUtils.isEmpty(shareData.payAmountDesc)){
            return null;
        }
        return new ShareBitmapUtils.Builder(R.drawable.bg_pay_for_another_share, context)
                .setShopName(shareData.shopName)
                .setPrice(shareData.payAmountDesc)
                .setTime(shareData.createTimeDesc)
                .build()
                .generateBitmap(context);
    }

    /**
     * 绘制店铺名称
     * @param paint
     * @param end
     * @param shopName
     * @param canvas
     */
    private void drawShopName(Paint paint, int end, String shopName, Canvas canvas) {
        paint.setTextSize(22);
        paint.setColor(Color.parseColor("#9A3C14"));
        PointF shopNamePointF = getTextStartPoint(end, shopName, paint, new Pair<>(138f, 160f));
        canvas.drawText(shopName, shopNamePointF.x, shopNamePointF.y, paint);
    }

    /**
     * 绘制时间
     * @param paint
     * @param end
     * @param time
     * @param canvas
     */
    private void drawTime(Paint paint, int end, String time, Canvas canvas) {
        paint.setTextSize(22);
        paint.setColor(Color.parseColor("#9A3C14"));
        PointF shopNamePointF = getTextStartPoint(end, time, paint, new Pair<>(193f, 215f));
        canvas.drawText(time, shopNamePointF.x, shopNamePointF.y, paint);
    }

    /**
     * 绘制价格
     * @param paint
     * @param end
     * @param price
     * @param canvas
     */
    private void drawPrice(Paint paint, int end, String price, Canvas canvas) {
        paint.setTextSize(36);
        paint.setColor(Color.parseColor("#FF172D"));
        paint.setFakeBoldText(true);
        Pair<Float, Float> pair = new Pair<>(250f, 272f);
        PointF shopNamePointF = getTextStartPoint(end, price, paint, pair);
        canvas.drawText(price, shopNamePointF.x, shopNamePointF.y, paint);
        Paint.FontMetrics priceMetrics = paint.getFontMetrics();
        paint.setTextSize(18);
        drawSymbol(paint, shopNamePointF.x, "￥", canvas, shopNamePointF.y);
    }

    /**
     * 绘制符号（￥）
     * @param paint
     * @param textStart
     * @param symbol
     * @param canvas
     * @param symbolBaseLine
     */
    private void drawSymbol(Paint paint, float textStart, String symbol, Canvas canvas, float symbolBaseLine) {
        paint.setTextSize(18);
        float symbolWidth = paint.measureText(symbol);
        float symbolStart = textStart - symbolWidth;
        Paint.FontMetrics metrics = paint.getFontMetrics();
        canvas.drawText(symbol, symbolStart, symbolBaseLine, paint);
    }

    /**
     * 获取绘制文字的起点
     * @param textEndX
     * @param text
     * @param paint
     * @param titleTB
     * @return
     */
    private PointF getTextStartPoint(int textEndX, String text, Paint paint, Pair<Float, Float> titleTB) {
        float textWidth = paint.measureText(text);
        float textStartX = textEndX - textWidth;
        float titleMiddleLine = (titleTB.second - titleTB.first) / 2 + titleTB.first;
        Paint.FontMetrics fontMetrics = paint.getFontMetrics();
        float textBaseLine = getBaseline(fontMetrics);
        float textBaseLineWithTitle = textBaseLine + titleMiddleLine;
        return new PointF(textStartX, textBaseLineWithTitle);
    }

    /**
     * 获取baseLine
     * @param fontMetrics
     * @return
     */
    private static float getBaseline(Paint.FontMetrics fontMetrics) {
        return (fontMetrics.descent - fontMetrics.ascent) / 2 - fontMetrics.descent;
    }

    public static class PayAnotherShareData {
        public String shopName;
        public String payAmountDesc;
        public String createTimeDesc;

        public PayAnotherShareData(String shopName, String payAmountDesc, String createTimeDesc) {
            this.shopName = shopName;
            this.payAmountDesc = payAmountDesc;
            this.createTimeDesc = createTimeDesc;
        }

        public PayAnotherShareData() {}
    }
}
