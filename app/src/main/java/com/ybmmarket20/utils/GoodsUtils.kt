package com.ybmmarket20.utils

import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import com.ybmmarket20.view.homesteady.getPriceStringBuilder

/**
 * 获取拼团商品价格(xxx.xx起)
 * @param isStepPrice 是否是阶梯价
 * @param minSkuPrice 阶梯价
 */
fun getGoodsSpellGroupStepPrice(isStepPrice: Boolean?, minSkuPrice: String?, assemblePrice: String?): SpannableStringBuilder? {
    return try {
        val price = if (isStepPrice == true) {
            //多阶梯
            if (minSkuPrice == null) return null
            val minSuffix = SpannableStringBuilder("起")
            val rangePriceSpannable = getPriceStringBuilder(minSkuPrice)
            if (rangePriceSpannable != null) {
                minSuffix.setSpan(AbsoluteSizeSpan(8, true), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                rangePriceSpannable.append(minSuffix)
            } else null
        } else {
            if (assemblePrice == null) return null
            getPriceStringBuilder(assemblePrice)
        }
        price
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

/**
 * 生成价格单位
 * @param unit 单位文本
 * @param isStepPrice 是否是阶梯价
 */
fun generateUnit(unit: String, isStepPrice: Boolean): String {
    return if (isStepPrice) {
        if (TextUtils.isEmpty(unit)) "起" else "起/$unit"
    } else {
        if (TextUtils.isEmpty(unit)) "元" else "元/$unit"
    }
}

fun getPriceStringBuilder(priceStr: String?): SpannableStringBuilder? {
    return getPriceStringBuilder3(priceStr, 7, 13, 7);
}

fun getPriceStringBuilder3(priceStr: String?, size1: Int, size2: Int, size3: Int): SpannableStringBuilder? {
    return priceStr?.let {
        val price = if (!priceStr.contains(".")) "$it.00" else it
        val spannable0 = SpannableStringBuilder("￥")
        spannable0.setSpan(AbsoluteSizeSpan(size1, true), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        val spannable1 = SpannableStringBuilder("${price.split(".")[0]}.")
        spannable1.setSpan(AbsoluteSizeSpan(size2, true), 0, spannable1.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        val spannable2 = SpannableStringBuilder(price.split(".")[1])
        spannable2.setSpan(AbsoluteSizeSpan(size3, true), 0, spannable2.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        spannable0.append(spannable1).append(spannable2)
    }
}