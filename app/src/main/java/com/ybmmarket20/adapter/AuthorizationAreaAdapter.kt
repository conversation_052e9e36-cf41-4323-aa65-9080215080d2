package com.ybmmarket20.adapter

import android.content.Intent
import android.widget.LinearLayout
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.AUTHORIZATION_AREA_AUTHORIZATIONED
import com.ybmmarket20.activity.AuthorizationDetailActivity
import com.ybmmarket20.bean.AuthorizationAreaRowBean

/**
 * 代下单授权Adapter
 */
class AuthorizationAreaAdapter(
        val resId: Int,
        data: List<AuthorizationAreaRowBean>?
) : YBMBaseAdapter<AuthorizationAreaRowBean>(resId, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: AuthorizationAreaRowBean) {
        baseViewHolder.setText(R.id.tv_authorization_title, t.title)
        baseViewHolder.setText(R.id.tv_authorization_submitter, t.applicant)
        baseViewHolder.setText(R.id.tv_authorization_submittime, t.createTime)
        baseViewHolder.setGone(R.id.iv_read_flag, t.readFlag == 0)
        baseViewHolder.setText(R.id.tv_authorization_status, if (t.status == AUTHORIZATION_AREA_AUTHORIZATIONED) "已授权" else "未授权")
        baseViewHolder.getConvertView().setOnClickListener {
            mContext.startActivity(Intent(mContext, AuthorizationDetailActivity::class.java)
                    .putExtra("authorizeBean", t))
        }
    }

}