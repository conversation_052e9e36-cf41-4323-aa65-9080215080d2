package com.ybmmarket20.adapter;

import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybm.app.utils.BugUtil;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.PopMerchantsBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

public class PopMerchantsAdapter extends YBMBaseAdapter<PopMerchantsBean> {

    private int tab;
    private PopMerchantsItemAdapter adapter;

    public PopMerchantsAdapter(int layoutResId, List<PopMerchantsBean> data, int tab) {
        super(layoutResId, data);
        this.tab = tab;
    }

    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, PopMerchantsBean rowsBean) {

        ybmBaseHolder.setText(R.id.tv_name,rowsBean.orgName).setText(R.id.tv_putaway,"上架"+rowsBean.upSkuNum+"种")
                .setText(R.id.tv_sale, mContext.getResources().getString(R.string.sale_sku_num, UiUtils.getNumber(rowsBean.saleSkuNum + "")));


        ybmBaseHolder.setOnClickListener(R.id.ll_company_name, new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                RoutersUtils.open("ybmpage://shopactivity?orgId=" + rowsBean.orgId);
            }
        });
        ImageView image = ybmBaseHolder.getView(R.id.iv_image);

        //标签
        if (TextUtils.isEmpty(rowsBean.orgLogo)) {
            ImageHelper.with(mContext).load(R.drawable.transparent).into(image);
        } else {
            String markerUrl = rowsBean.orgLogo;
            if (!markerUrl.startsWith("http")) {
                markerUrl = AppNetConfig.LORD_TAG + markerUrl;
            }
            ImageHelper.with(mContext).load(markerUrl).placeholder(R.drawable.transparent)
                    .error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE)
                    .dontAnimate().dontTransform().into(image);
        }

        RecyclerView listView = ybmBaseHolder.getView(R.id.rv_list);

        adapter = new PopMerchantsItemAdapter(rowsBean.skuVOs);
        adapter.setEnableLoadMore(false);

        listView.setNestedScrollingEnabled(false);
        listView.setLayoutManager(new WrapLinearLayoutManager(mContext, LinearLayout.HORIZONTAL, false));
        listView.setAdapter(adapter);

    }
}
