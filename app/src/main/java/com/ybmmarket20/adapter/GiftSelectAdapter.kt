package com.ybmmarket20.adapter

import android.graphics.Paint
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.lifecycle.MutableLiveData
import com.bumptech.glide.Glide
import com.luck.picture.lib.tools.DoubleUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.GiftSelectNormalBean
import com.ybmmarket20.bean.GiftSelectStatus
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundConstraintLayout
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import kotlin.math.max

/**
 * @class   GiftSelectAdapter
 * <AUTHOR>
 * @date  2024/9/13
 * @description
 */
class GiftSelectAdapter(val mGiveUpLiveData: MutableLiveData<Boolean>) :YBMBaseMultiItemAdapter<GiftSelectNormalBean>(arrayListOf()) {

    init {
        addItemType(GIFT_SELECT_NORMAL_TYPE, R.layout.item_gift_select_normal)
    }

    companion object{
        const val GIFT_SELECT_NORMAL_TYPE = 1 //正常选择赠品的样式
    }

    var canSelectNumber:Int = 0 //可选数量

    var selectGoodsCallBack:((Pair<Boolean,GiftSelectNormalBean>,position:Int)->Unit)? = null

    var changeAmountCallBack:((GiftSelectNormalBean,amount:Int,position:Int)->Unit)? = null

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, mBean: GiftSelectNormalBean) {
        baseViewHolder?.apply {
            when(mBean.itemType){
                GIFT_SELECT_NORMAL_TYPE ->{
                    bindNormalGiftSelect(this,mBean)
                }
                else ->{}
            }
        }

    }

    private fun bindNormalGiftSelect(holder: YBMBaseHolder, mBean: GiftSelectNormalBean) {
        holder.itemView.apply {
            val tvTitle = findViewById<TextView>(R.id.tv_title)
            val tvValidity = findViewById<TextView>(R.id.tv_validity)
            val tvPrice = findViewById<TextView>(R.id.tv_price)
            val tvUnit = findViewById<TextView>(R.id.tv_unit)
            val tvOriginPrice = findViewById<TextView>(R.id.tv_origin_price)
            val tvNumber = findViewById<TextView>(R.id.tv_number)
            val ivGift = findViewById<ImageView>(R.id.iv_gift)
            val ivSelect = findViewById<ImageView>(R.id.iv_select)
            val tvLimit = findViewById<TextView>(R.id.tv_limit)
            val tvNoInventory = findViewById<TextView>(R.id.tv_no_inventory)

            val ivMinus = findViewById<ImageView>(R.id.iv_minus)
            val ivAdd = findViewById<ImageView>(R.id.iv_add)
            val clNumber = findViewById<RoundConstraintLayout>(R.id.cl_number)

            val showPriceStr = SpannableStringBuilder()

            mContext.glideLoadWithPlaceHolder((AppNetConfig.LORD_IMAGE + mBean.imageUrl),ivGift)

            tvTitle.text = (mBean.showName?:"")+(mBean.spec?:"")
            tvValidity.text = "效期: " + mBean.nearEffect
            tvUnit.text = mBean.productUnit?.let{ "/$it"}?:""
            showPriceStr.append("¥")
            showPriceStr.append(mBean.actPrice ?:"0.01")
            showPriceStr.setSpan(AbsoluteSizeSpan(12, true), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            tvPrice.text = showPriceStr


            tvOriginPrice.apply {
                text = "¥"+mBean.fob.toString()
                paint.flags = Paint.STRIKE_THRU_TEXT_FLAG //中划线
            }

            //初始化默认下标
            mBean.selectNum = mBean.selectNum?.let {
                if (it <=1){
                    if ((mBean.orderGiveMinQty?:0) > 0 ) {
                        mBean.orderGiveMinQty
                    }else
                        1
                }else
                    it
            }?:1

            tvNumber.text = mBean.selectNum.toString()
            if (mBean.orderGiveMinQty == 0 && mBean.orderGiveMaxQty == 0){
                tvLimit.isVisible = false
            }else{
                tvLimit.isVisible = true
                var minStr = ""
                var maxStr = ""
                if ((mBean.orderGiveMinQty?:0) > 0){
                    minStr = "${mBean.orderGiveMinQty}盒起送"
                }
                if ((mBean.orderGiveMaxQty?:0)> 0){
                    maxStr = "限${mBean.orderGiveMaxQty}盒"
                }
                tvLimit.text = if (minStr.isNotEmpty() && maxStr.isNotEmpty()){
                     "${minStr}，${maxStr}"
                }else if (minStr.isNotEmpty()){
                    minStr
                }else if (maxStr.isNotEmpty()){
                    maxStr
                }else{
                    ""
                }
            }

            //售罄
            if (mBean.isSellOut()){
                tvNoInventory.visibility = View.VISIBLE
                ivGift.alpha = 0.5f
                clNumber.isVisible = false
                tvLimit.isVisible = false
            }else{
                tvNoInventory.visibility = View.GONE
                ivGift.alpha = 1f
                ivSelect.setOnClickListener(null)
                clNumber.isVisible = true
                tvLimit.isVisible = true
            }


            when(mBean.selectStatus.value){
                GiftSelectStatus.NO_SELECT ->{
                    Glide.with(mContext).load(R.drawable.icon_gift_no_select).into(ivSelect)
                    ivSelect.setOnClickListener {
                        if (mGiveUpLiveData.value == true) return@setOnClickListener
                        val nowSelectGiftNumber = getSelectGiftNumber()
                        if (nowSelectGiftNumber+(mBean.selectNum?:1) <= canSelectNumber){
                            selectGoodsCallBack?.invoke(Pair(true,mBean),holder.adapterPosition)
                        }else{
                            ToastUtils.showShort(mContext.getString(R.string.str_gift_select_toast_2,canSelectNumber.toString()))
                        }
                    }
                }

                GiftSelectStatus.SELECTED ->{
                    Glide.with(mContext).load(R.drawable.icon_gift_select).into(ivSelect)
                    ivSelect.setOnClickListener {
                        if (mGiveUpLiveData.value == true) return@setOnClickListener
                        selectGoodsCallBack?.invoke(Pair(false,mBean),holder.adapterPosition)
                    }

                }

                GiftSelectStatus.CANT_SELECT->{
                    Glide.with(mContext).load(R.drawable.icon_gift_cant_select).into(ivSelect)
                    ivSelect.setOnClickListener(null)
                }

                else -> {}
            }

            //下限
            val min = if ((mBean.orderGiveMinQty?:0) > 0 ) {
                mBean.orderGiveMinQty?:1
            }else 1

            if ((mBean.selectNum ?: 1) <= min){
                ivMinus.alpha = 0.5f
                ivMinus.setOnClickListener(null)
            }else{
                ivMinus.alpha = 1f
                ivMinus.setOnClickListener {
                    if (mGiveUpLiveData.value == true) return@setOnClickListener
                    if (DoubleUtils.isFastDoubleClick()) return@setOnClickListener

                    val step = 1 //目前需求固定加减1
                    val amount = max((mBean.selectNum?:1).toInt()-step,1)
                    val nowSelectGiftNumber = getSelectGiftNumber()
                    val needNumber = if (mBean.selectStatus.value == GiftSelectStatus.SELECTED) nowSelectGiftNumber-step else nowSelectGiftNumber+amount
                    if (needNumber <= canSelectNumber){
                        changeAmountCallBack?.invoke(mBean,amount,holder.adapterPosition)
                    }else{
                        ToastUtils.showShort(mContext.getString(R.string.str_gift_select_toast_2,canSelectNumber.toString()))
                    }

                }
            }

            ivAdd.setOnClickListener {
                if (mGiveUpLiveData.value == true) return@setOnClickListener
                if (DoubleUtils.isFastDoubleClick()) return@setOnClickListener

                val step = 1 //目前需求固定加减1
                val nowAmount = (mBean.selectNum?:1).toInt()
                val amount = nowAmount+step
                val nowSelectGiftNumber = getSelectGiftNumber()
                val needNumber = if (mBean.selectStatus.value == GiftSelectStatus.SELECTED) nowSelectGiftNumber+step else nowSelectGiftNumber+amount

                //先判断上限逻辑
                mBean.orderGiveMaxQty?.let {
                    if (it in 1 until amount){
                        ToastUtils.showShort(mContext.getString(R.string.str_gift_select_toast_3))
                        return@setOnClickListener
                    }
                }

                if (needNumber <= canSelectNumber){
                    changeAmountCallBack?.invoke(mBean,amount,holder.adapterPosition)
                }else{
                    ToastUtils.showShort(mContext.getString(R.string.str_gift_select_toast_2,canSelectNumber.toString()))
                }

            }

        }
    }

    /**
     * 获取目前选中的赠品数
     * @return Int
     */
    fun getSelectGiftNumber():Int{
        var selectNumber = 0
        (mData as ArrayList<GiftSelectNormalBean>).forEach {
            if (it.selectStatus.value == GiftSelectStatus.SELECTED){
                selectNumber += (it.selectNum ?: 0)
            }
        }

        return selectNumber
    }


}