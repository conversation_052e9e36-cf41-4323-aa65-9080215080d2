package com.ybmmarket20.adapter;

import android.text.Html;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.util.Linkify;
import android.view.View;
import android.widget.TextView;

import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.OrderDeliveryLogisticsDetailList;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/*
 * 物流分组
 * */
public class LogisticsGroupListAdapter extends YBMGroupListAdapter<OrderDeliveryLogisticsDetailList> {

    private SimpleDateFormat dateFormat;
    private int getLayoutPosition = -1;

    public LogisticsGroupListAdapter(List<OrderDeliveryLogisticsDetailList> data) {
        super(R.layout.logistics_item_group, R.layout.logistics_item_content, data);
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
    }

    @Override
    public void bindGroupView(YBMBaseHolder ybmBaseHolder, OrderDeliveryLogisticsDetailList logisticsList) {

        ybmBaseHolder.setText(R.id.tv_time01, getTime(logisticsList.createDateCustomOne))
                .setText(R.id.tv_time02, getTime(logisticsList.createDateCustomTwo))
                .setText(R.id.tv_waybill, "运单号:" + logisticsList.waybillNo)
                .setText(R.id.tv_is_sign, logisticsList.isSign == 1 ? "已签收" : "未签收")
                .setGone(R.id.tv_is_sign, logisticsList.isSign == 1);

        TextView tvContent01 = ybmBaseHolder.getView(R.id.tv_content01);
        setLinkMask(tvContent01, logisticsList.descriptionCustomOne);

        TextView tvContent02 = ybmBaseHolder.getView(R.id.tv_content02);
        setLinkMask(tvContent02, logisticsList.descriptionCustomTwo);

        //加载跟多按钮根据getSubItems有无数据isExpanded状态显示
        if (logisticsList.getSubItems() != null && logisticsList.getSubItems().size() > 0) {
            ybmBaseHolder.getView(R.id.ll_more).setVisibility(logisticsList.isExpanded() ? View.GONE : View.VISIBLE);
        } else {
            ybmBaseHolder.getView(R.id.ll_more).setVisibility(View.GONE);
        }

        ybmBaseHolder.getView(R.id.ll_item02).setVisibility(!TextUtils.isEmpty(logisticsList.descriptionCustomTwo) ? View.VISIBLE : View.GONE);
        ybmBaseHolder.getView(R.id.ll_title).setVisibility(!TextUtils.isEmpty(logisticsList.waybillNo) ? View.VISIBLE : View.GONE);

        ybmBaseHolder.getView(R.id.group_line01).setVisibility(!TextUtils.isEmpty(logisticsList.descriptionCustomTwo) ? View.VISIBLE : View.INVISIBLE);
        ybmBaseHolder.getView(R.id.group_line02).setVisibility((logisticsList.getSubItems() != null && logisticsList.getSubItems().size() > 0) ? View.VISIBLE : View.INVISIBLE);

        //展开状态根据是否只有一条物流数据展示
        ybmBaseHolder.getView(R.id.ll_root).setEnabled(!(logisticsList.isExpanded() && mData.size() == 3));
        ybmBaseHolder.setOnClickListener(R.id.ll_root, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //是否一条物流信息下有多条数据
                if (logisticsList.getSubItems() != null && logisticsList.getSubItems().size() > 0) {
                    //展开状态
                    if (logisticsList.isExpanded()) {
                        //collapse(ybmBaseHolder.getLayoutPosition());
                    } else {
                        //collapseAll(true);
                        if (getLayoutPosition == ybmBaseHolder.getLayoutPosition()) {
                            return;
                        }
                        collapse_(getLayoutPosition);
                        tvContent01.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                expand_(ybmBaseHolder.getLayoutPosition());
                                getLayoutPosition = ybmBaseHolder.getLayoutPosition();
                            }
                        },200);
                    }
                }
            }
        });

    }

    @Override
    public void bindContentView(YBMBaseHolder ybmBaseHolder, OrderDeliveryLogisticsDetailList logisticsList) {

        ybmBaseHolder.getView(R.id.line).setVisibility(!logisticsList.isLast ? View.VISIBLE : View.INVISIBLE);
        ybmBaseHolder.setText(R.id.tv_time, getTime(logisticsList.deliveryTime));

        TextView tvContent = ybmBaseHolder.getView(R.id.tv_content);
        setLinkMask(tvContent, logisticsList.description);
    }

    //富文本配置a标签电话号码可点击
    private void setLinkMask(TextView tvContent01, String spanned) {
        if (!TextUtils.isEmpty(spanned) && spanned.length() > 0) {
            tvContent01.setAutoLinkMask(Linkify.ALL);
            tvContent01.setMovementMethod(LinkMovementMethod.getInstance());
            tvContent01.setText(Html.fromHtml(spanned));
        }
    }

    private String getTime(long time) {
        String createTime = null;
        try {
            createTime = dateFormat.format(new Date(time));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return createTime;
    }
}
