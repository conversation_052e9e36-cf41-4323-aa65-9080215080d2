package com.ybmmarket20.adapter

import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.widget.ImageView
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.PurchaseRowsBean
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 采购单列表
 */
class PurchaseReconciliationAdapter(data: List<PurchaseRowsBean>) :
    YBMBaseAdapter<PurchaseRowsBean>(R.layout.item_purchase_reconciliation, data) {
    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: PurchaseRowsBean?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            val ivShop = holder.getView<ImageView>(R.id.iv_shop)
            val tvShopName = holder.getView<TextView>(R.id.tv_shop_name)
            val tvShopAddress = holder.getView<TextView>(R.id.tv_shop_address)
            val tvAmount = holder.getView<TextView>(R.id.tv_amount)
            ImageUtil.load(mContext, bean.shopLogo, ivShop)
            tvShopName.text = bean.shopName
            tvShopAddress.text = bean.orgName
            SpannableStringBuilder("¥${UiUtils.transform(bean.orderTotalAmount)}").also {
                it.setSpan(AbsoluteSizeSpan(9, true), 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            }.let(tvAmount::setText)

        }
    }
}