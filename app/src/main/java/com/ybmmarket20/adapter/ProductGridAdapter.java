package com.ybmmarket20.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.TagView;

import java.util.ArrayList;
import java.util.List;


/**
 * 首页动态布局
 */
public class ProductGridAdapter extends BaseAdapter {

    public List<RowsBean> comments;
    private Context mContext;
    private int layoutType = 0;// 1

    public ProductGridAdapter(Context mContext, List<RowsBean> comments) {
        super();
        this.mContext = mContext;
        this.comments = comments;
    }

    public ProductGridAdapter(Context mContext, List<RowsBean> comments, int layoutType) {
        super();
        this.mContext = mContext;
        this.comments = comments;
        this.layoutType = layoutType;
    }

    @Override
    public int getCount() {
        return comments == null ? 0 : comments.size();
    }

    @Override
    public Object getItem(int position) {
        return comments.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        final RowsBean rowsBean = comments.get(position);
        ViewHolder holder = null;

        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.home_product_item, parent, false);
            holder = new ViewHolder();
            holder.iv_product = (ImageView) convertView.findViewById(R.id.iv_product);
            holder.tvName = (TextView) convertView.findViewById(R.id.tv_name);
            holder.tvPrice = (TextView) convertView.findViewById(R.id.tv_price);
            holder.tvSpec = (TextView) convertView.findViewById(R.id.tv_spec);
            holder.home_time_bg = (ImageView) convertView.findViewById(R.id.home_time_bg);
            holder.iv_tag_left = (ImageView) convertView.findViewById(R.id.iv_tag_left);
            holder.iv_tag_left = (ImageView) convertView.findViewById(R.id.iv_tag_left);
            holder.rlIconType = (TagView) convertView.findViewById(R.id.rl_icon_type);
            holder.tvExclusive = (TextView) convertView.findViewById(R.id.tv_exclusive);
            holder.tv_health_insurance = (TextView) convertView.findViewById(R.id.tv_health_insurance);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        if (rowsBean.getMarkerUrl() != null && rowsBean.getMarkerUrl().startsWith("http")) {
            ImageHelper.with(mContext).load(rowsBean.getMarkerUrl()).placeholder(R.drawable.transparent).error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(holder.iv_tag_left);
        } else {
            if (TextUtils.isEmpty(rowsBean.getMarkerUrl())) {
                ImageHelper.with(mContext).load(R.drawable.transparent).into(holder.iv_tag_left);
            } else {
                ImageHelper.with(mContext).load(AppNetConfig.LORD_TAG + rowsBean.getMarkerUrl()).placeholder(R.drawable.transparent).error(R.drawable.transparent).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(holder.iv_tag_left);
            }
        }
        ImageHelper.with(mContext).load(AppNetConfig.LORD_IMAGE + rowsBean.getImageUrl()).placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontAnimate().dontTransform().into(holder.iv_product);
        holder.tvName.setText(rowsBean.getShowName());
        holder.tvSpec.setText(rowsBean.getSpec());
        //商品区间价格
        holder.tvPrice.setText(UiUtils.showProductPrice(rowsBean));
        //  LogUtils.d("status=="+rowsBean.getStatus()+"\n getIsControlPriceToMe="+rowsBean.getIsControlPriceToMe()+"\n rowsBean.getIsBuy="+rowsBean.getIsBuy());
        //是否促销价格
        if (rowsBean.getStatus() == 3 || rowsBean.getStatus() == 5) {
            holder.tvPrice.setText(String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
        }

        //是否控销  isControl=1表示控销，=2表示不是控销
        else if (rowsBean.getIsControl() == 1) {

            //控销可购买
            if (rowsBean.isPurchase()) {
                if (rowsBean.getPriceType() == 1) {
                    holder.tvPrice.setText(String.valueOf("¥" + UiUtils.transform(rowsBean.getFob())));
                } else {
                    holder.tvPrice.setText(UiUtils.showProductPrice(rowsBean));
                }
            } else {
                //控销不可购买
                holder.tvPrice.setText("暂无购买权限");
            }
        }

        if (rowsBean.getStatus() == 2 || rowsBean.getStatus() == 4) {
            holder.home_time_bg.setVisibility(View.VISIBLE);
            holder.tvPrice.setTextColor(UiUtils.getColor(R.color.main_text));
        } else {
            holder.home_time_bg.setVisibility(View.INVISIBLE);
            holder.tvPrice.setTextColor(UiUtils.getColor(R.color.record_red));
        }

        convertView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mOnItemClickListener != null) {
                    mOnItemClickListener.onItemClick(rowsBean);
                }
            }
        });
        //是否显示独家标示
        holder.tvExclusive.setVisibility(rowsBean.getAgent() == 1?View.VISIBLE:View.GONE);
        holder.tv_health_insurance.setVisibility(rowsBean.getIsUsableMedicalStr() == 1?View.VISIBLE:View.GONE);

        TagView rlIconType =  holder.rlIconType;
        rlIconType.bindData(rowsBean.getTagList(),2);

        return convertView;
    }

    private final class ViewHolder {
        public ImageView iv_product;
        public TextView tvName;
        public TextView tvPrice;
        public TextView tvSpec;
        public TagView rlIconType;
        public TextView tvExclusive;
        public ImageView home_time_bg;
        public ImageView iv_tag_left;
        public TextView tv_health_insurance;
    }

    public static interface OnGridviewItemClickListener {
        void onItemClick(RowsBean rows);
    }

    private OnGridviewItemClickListener mOnItemClickListener = null;

    public void setOnItemClickListener(OnGridviewItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }

}
