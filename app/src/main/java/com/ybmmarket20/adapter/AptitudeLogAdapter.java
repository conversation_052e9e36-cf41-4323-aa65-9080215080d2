package com.ybmmarket20.adapter;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.AptitudeLogListBean;
import com.ybmmarket20.utils.DateTimeUtil;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.utils.UiUtils;

import java.util.List;

public class AptitudeLogAdapter extends YBMBaseAdapter<AptitudeLogListBean> {
    public static final String STATUS_PASS="1";
    public static final String STATUS_REJECT="0";

    public AptitudeLogAdapter(int layoutResId, List<AptitudeLogListBean> data) {
        super(layoutResId, data);
    }


    @Override
    protected void bindItemView(YBMBaseHolder ybmBaseHolder, AptitudeLogListBean bean) {
        if (ybmBaseHolder.getAdapterPosition() == 0) {

            ybmBaseHolder.setImageResource(R.id.iv_circle, R.drawable.shap_aptitude_statues_circle);

            View view = ybmBaseHolder.getView(R.id.iv_circle);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            layoutParams.width = UiUtils.dp2px(15);
            layoutParams.height = UiUtils.dp2px(15);
            if (getData().size() != 1) {

                ybmBaseHolder.getView(R.id.iv_line).setVisibility(View.VISIBLE);
            } else {
                ybmBaseHolder.getView(R.id.iv_line).setVisibility(View.GONE);
            }
        } else if (ybmBaseHolder.getAdapterPosition() == getData().size() - 1) {
            ybmBaseHolder.getView(R.id.iv_line).setVisibility(View.GONE);
            ybmBaseHolder.setImageResource(R.id.iv_circle, R.drawable.shap_aptitude_statues_circle_unchecked);
        } else {
            View view = ybmBaseHolder.getView(R.id.iv_circle);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            layoutParams.width = UiUtils.dp2px(11);
            layoutParams.height = UiUtils.dp2px(11);
            View ll = ybmBaseHolder.getView(R.id.ll_log_line);
            ll.setPadding(0, UiUtils.dp2px(6), 0, 0);
            ybmBaseHolder.setImageResource(R.id.iv_circle, R.drawable.shap_aptitude_statues_circle_unchecked);
            ybmBaseHolder.getView(R.id.iv_line).setVisibility(View.VISIBLE);
        }

        String time = DateTimeUtil.getLogDateTime(bean.handTime);

        ybmBaseHolder.setText(R.id.tv_audit_times, "【" + bean.nodeName + "】");
        ybmBaseHolder.setText(R.id.tv_auditTime, time);
        ybmBaseHolder.setText(R.id.tv_name, bean.handler);
        ybmBaseHolder.setText(R.id.tv_operation, bean.handStatusStr);
        ybmBaseHolder.setTextColor(R.id.tv_operation, UiUtils.getColorFromLogStatus(bean.handStatus));

        ybmBaseHolder.setText(R.id.tv_cause, bean.handDetail);

        ybmBaseHolder.setGone(R.id.tv_name, !TextUtils.isEmpty(bean.handler));
        ybmBaseHolder.setGone(R.id.tv_operation, !TextUtils.isEmpty(bean.handStatusStr));
        ybmBaseHolder.getView(R.id.ll_cause_c).setVisibility(STATUS_REJECT.equals(bean.handStatus) ? View.VISIBLE : View.GONE);
        ybmBaseHolder.setGone(R.id.tv_auditTime,bean.handTime!=0);
    }
}
