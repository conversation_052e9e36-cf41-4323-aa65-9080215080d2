package com.ybmmarket20.adapter;

import android.text.TextUtils;

import com.ybm.app.adapter.YBMBaseHolder;
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.LabelIconBean;
import com.ybmmarket20.bean.RefundProductListBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.StringUtil;
import com.ybmmarket20.view.TagView;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单详情商品明细列表
 * 入库价格明细adapter
 */

public class OrderPriceAdapter extends YBMBaseMultiItemAdapter<RefundProductListBean> {

    private int mTotal;

    public OrderPriceAdapter(List<RefundProductListBean> data) {
        super(data);
        addItemType(RefundProductListBean.ITEMTYPE_PACKAGE_TITLE, R.layout.detail_product_package_title3);
        addItemType(RefundProductListBean.ITEMTYPE_PACKAGE_SUBTITLE, R.layout.detail_product_package_subtitle3);
        addItemType(RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT, R.layout.order_product_price_item);
        addItemType(RefundProductListBean.ITEMTYPE_CONTENT, R.layout.order_product_price_item);
        addItemType(RefundProductListBean.ITEMTYPE_GIFT_CONTENT, R.layout.detail_product_gift_item);
        initData();
    }

    @Override
    protected void bindItemView(YBMBaseHolder baseViewHolder, RefundProductListBean bean) {
        switch (bean.getItemType()) {
            case RefundProductListBean.ITEMTYPE_PACKAGE_TITLE:
                bindPackgeTitle(baseViewHolder, bean);
                break;
            case RefundProductListBean.ITEMTYPE_PACKAGE_SUBTITLE:
                bindPackgeSubTitle(baseViewHolder, bean);
                break;
            case RefundProductListBean.ITEMTYPE_GIFT_CONTENT:
                bindGiftSubTitle(baseViewHolder, bean);
                break;
            case RefundProductListBean.ITEMTYPE_PACKAGE_CONTENT:
            case RefundProductListBean.ITEMTYPE_CONTENT:
                bindItem(baseViewHolder, bean);
                break;
        }
    }

    // 初始化isSelected的数据
    private void initData() {
        mTotal = 0;
        if (mData != null && mData.size() > 0) {
            for (int i = 0; i < mData.size(); i++) {
                RefundProductListBean listBean = (RefundProductListBean) mData.get(i);
                if (listBean.type == 5) {
                    mTotal++;
                }
            }
        }
    }

    private void bindGiftSubTitle(YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {

        baseViewHolder.setGone(R.id.tv_btn_ok, false);
        baseViewHolder.setGone(R.id.cb_choice, false);
        String Str = "物料心愿单礼包";
        String StrNumber = Str + "(" + mTotal + ")";
        baseViewHolder.setText(R.id.tv_number, StrNumber);
    }


    protected void bindPackgeTitle(YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {

//        baseViewHolder.getView(R.id.cb_choice).setVisibility(View.GONE);
//        TextView tvName = baseViewHolder.getView(R.id.tv_name);
//        tvName.setText(bean.productName);
//        baseViewHolder.setText(R.id.tv_num, "X" + String.valueOf(bean.productAmount)).setText(R.id.tv_name,bean.productName);
    }

    protected void bindPackgeSubTitle(YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {
//        baseViewHolder.setText(R.id.tv_price, "单价:¥" + String.valueOf(bean.productPrice)).setText(R.id.tv_total,"小计:¥" +bean.subtotal);
    }

    protected void bindItem(final YBMBaseHolder baseViewHolder, final RefundProductListBean bean) {
        baseViewHolder.setImageUrl(R.id.iv_order, AppNetConfig.LORD_IMAGE + bean.imageUrl, R.drawable.jiazaitu_min);
        baseViewHolder.setText(R.id.tv_name, bean.productName);
        baseViewHolder.setText(R.id.tv_price, "规格:" + bean.spec);
        TagView tagView = baseViewHolder.getView(R.id.tg);
        if (!TextUtils.isEmpty(bean.blackProductText) && (bean.tagList == null || bean.tagList.isEmpty())) {
            LabelIconBean bean1 = new LabelIconBean();
            bean1.name = bean.blackProductText;
            bean1.uiType = 2;
            List<LabelIconBean> tagList = new ArrayList<>();
            tagList.add(bean1);
            tagView.bindData(tagList);
        } else {
            tagView.bindData(bean.tagList);
        }
        baseViewHolder.setText(R.id.tv_num, "X" + String.valueOf(bean.productAmount));
        baseViewHolder.setText(R.id.tv_rel_price_value, "¥" + StringUtil.DecimalFormat2Double(bean.productPrice));//原价格
        baseViewHolder.setText(R.id.tv_order_coupon_value, "¥" + bean.getDiscountAmount());//优惠金额
        baseViewHolder.setText(R.id.tv_rebate_value, "¥" + bean.getBalanceAmount());//余额抵扣
        baseViewHolder.setText(R.id.tv_rk_value, "¥" + bean.getRkPrice());//实付价格

        baseViewHolder.setText(R.id.tv_rel_price_value_tow, "¥" + StringUtil.DecimalFormat2Double(bean.productPrice));//原价格
        baseViewHolder.setText(R.id.tv_order_coupon_value_tow, "¥" + bean.getDiscountAmount());//优惠金额
        baseViewHolder.setText(R.id.tv_rebate_value_tow, "¥" + bean.getBalance2Amount());//返利
        baseViewHolder.setText(R.id.tv_rk_value_tow, "¥" + bean.getFormatCostPrice());//成本价格
    }

}
