package com.ybmmarket20.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_INVOICE
import com.ybmmarket20.activity.afterSales.activity.TIPS_TYPE_LICENSE
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CheckOrderDetailBean
import com.ybmmarket20.bean.aftersales.AfterSalesDetailBean
import com.ybmmarket20.bean.aftersales.AfterSalesDetailStatusBean
import com.ybmmarket20.bean.aftersales.RefundInvoiceInfo
import com.ybmmarket20.network.request.AfterSalesDetailRequest
import com.ybmmarket20.network.request.OrderDetailRequest
import com.ybmmarket20.utils.RoutersUtils
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

/**
 * 售后详情
 */

//撤回售后申请
const val AFTER_SALES_OPERATION_TYPE_WITHDRAW = 2
//确认发票已退回
const val AFTER_SALES_OPERATION_TYPE_RETURN_INVOICE = 7

class AfterSalesDetailViewModel(appLike: Application) : BaseViewModel(appLike) {

    private val _afterSalesDetailLiveData = MutableLiveData<BaseBean<AfterSalesDetailBean>>()
    val afterSalesDetailLiveData: LiveData<BaseBean<AfterSalesDetailBean>> = _afterSalesDetailLiveData

    private val _customerServiceRouterLiveData = MutableLiveData<String>()
    val customerServiceRouterLiveData: LiveData<String> = _customerServiceRouterLiveData

    private val _errorInfoLiveData = MutableLiveData<String>()
    val errorInfoLiveData: LiveData<String> = _errorInfoLiveData

    private val _afterSaleTipsLiveData = MutableLiveData<String>()
    val afterSaleTipsLiveData: LiveData<String> = _afterSaleTipsLiveData

    //发票退回信息
    private val mRefundInvoiceInfo: RefundInvoiceInfo? = null

    fun getAfterSalesDetailInfo(afterSalesNo: String) {
        viewModelScope.launch {
            val detailDeferred = async {
                AfterSalesDetailRequest().getAfterSalesDetailInfo(afterSalesNo)
            }
            val statusDeferred = async {
                AfterSalesDetailRequest().getAfterSalesDetailStatus(afterSalesNo)
            }
            val detailResult = detailDeferred.await()
            val statusResult = statusDeferred.await()
            if (detailResult.isSuccess && statusResult.isSuccess) {
                detailResult.data.afterSalesDetailStatusBean = statusResult.data
                detailResult.data.refundInvoiceInfo = mRefundInvoiceInfo ?: RefundInvoiceInfo()
                detailResult.data.refundInvoiceInfo!!.isShowCancelBtn = detailResult.data.isShowCancelBtn
                detailResult.data.refundInvoiceInfo!!.auditProcessState = detailResult.data.auditProcessState
            }
            _afterSalesDetailLiveData.postValue(detailResult)
        }
    }

    /**
     * 获取三方店铺客服
     */
    fun getThirdCompanyCustomerServiceRouter() {
        if (_customerServiceRouterLiveData.value != null) {
            _customerServiceRouterLiveData.postValue(_customerServiceRouterLiveData.value)
            return
        }
        viewModelScope.launch {
            showLoading()
            _afterSalesDetailLiveData.value?.data?.also {afterSalesDetailBean->
                val imPackUrl = AfterSalesDetailRequest().getIMPackUrl("${afterSalesDetailBean.isThirdCompany}")
                if (imPackUrl.isSuccess) {
                    val router = if (afterSalesDetailBean.isThirdCompany == 1) { //商家客服
                        RoutersUtils.getRouterPopCustomerServiceUrl(
                            imPackUrl.data.IM_PACK_URL,
                            afterSalesDetailBean.orgId,
                            afterSalesDetailBean.orderNo,
                            afterSalesDetailBean.origName
                        )
                    } else { //平台客服
                        RoutersUtils.getRouterYbmOrderCustomerServiceUrl(
                            imPackUrl.data.IM_PACK_URL,
                            afterSalesDetailBean.orderNo
                        )
                    }
                    _customerServiceRouterLiveData.postValue(router)
                }
            }
            dismissLoading()
        }
    }

    /**
     * 撤回售后申请
     */
    fun withdrawAfterSaleApplication(){
        viewModelScope.launch {
            showLoading()
            val afterSalesDetailBean = _afterSalesDetailLiveData.value?.data?: return@launch
            val result = AfterSalesDetailRequest().afterSalesOperation(mapOf(
                "afterSalesNo" to (afterSalesDetailBean.afterSalesNo?: ""),
                "operateType" to "$AFTER_SALES_OPERATION_TYPE_WITHDRAW",
            ))
            if (result.isSuccess) {
                getAfterSalesDetailInfo(afterSalesDetailBean.afterSalesNo?: "")
            } else {
                dismissLoading()
            }
        }
    }

    /**
     * 确认发票已退回
     */
    fun returnInvoice() {
        viewModelScope.launch {
            showLoading()
            val afterSalesDetailBean = _afterSalesDetailLiveData.value?.data?: return@launch
            val refundInvoiceInfo = _afterSalesDetailLiveData.value?.data?.refundInvoiceInfo?: return@launch
            val errorInfo = if (refundInvoiceInfo.logisticsCompany.isNullOrEmpty()) {
                "请输入物流公司"
            } else if (refundInvoiceInfo.expressNo.isNullOrEmpty()) {
                "请输入快递单号"
            } else null
            errorInfo?.let {
                _errorInfoLiveData.postValue(it)
                return@launch
            }
            val result = AfterSalesDetailRequest().afterSalesOperation(mapOf(
                "afterSalesNo" to (afterSalesDetailBean.afterSalesNo?: ""),
                "operateType" to "$AFTER_SALES_OPERATION_TYPE_RETURN_INVOICE",
                "expressNo" to (refundInvoiceInfo.expressNo ?: ""),
                "expressName" to (refundInvoiceInfo.logisticsCompany ?: ""),
                "expressEvidence" to Gson().toJson(refundInvoiceInfo.images?: listOf<String>(), object: TypeToken<List<String>>(){}.type)
            ))

            if (result.isSuccess) {
                getAfterSalesDetailInfo(afterSalesDetailBean.afterSalesNo?: "")
            } else {
                dismissLoading()
            }
        }
    }

    /**
     * 更新倒计时
     */
    fun updateCountDown(): Long {
        val afterSalesDetailStatusBean: AfterSalesDetailStatusBean =
            _afterSalesDetailLiveData.value?.data?.afterSalesDetailStatusBean ?: return -1
        if (afterSalesDetailStatusBean.countDownTime <= 0) return -1
        afterSalesDetailStatusBean.countDownTime -= 1
        return afterSalesDetailStatusBean.countDownTime
    }


    fun getAfterSalesInfo(orderNo:String, orgId: String, orgName: String, tipsType: Int, tipsTitle: String = "") {
        viewModelScope.launch {
            showLoading()
            val afterSalesTips = OrderDetailRequest().getAfterSalesTips(orderNo, orgId)
            if (afterSalesTips.isSuccess) {
                val tipsContent = afterSalesTips.data
                val routerStr = if (!tipsContent.isNullOrEmpty()) {
                    //须知页面路由
                    "ybmpage://companyaftersalestips?orderNo=$orderNo&orgName=$orgName&contentTips=${RoutersUtils.paramsToBase64(tipsContent)}&tipsType=$tipsType"
                } else if (tipsType == TIPS_TYPE_INVOICE) {
                    //发票相关售后路由
                    "ybmpage://invoiceaftersalesservice?orderNo=$orderNo"
                } else if (tipsType == TIPS_TYPE_LICENSE) {
                    //选择需要补发的资质路由
                    "ybmpage://licenseaftersalesservice?orderNo=$orderNo&orgName=$orgName"
                } else {
                    ""
                }
                _afterSaleTipsLiveData.postValue(routerStr)
            }
            dismissLoading()
        }
    }

}