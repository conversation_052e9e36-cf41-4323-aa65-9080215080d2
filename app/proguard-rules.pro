# This is a configuration file for ProGuard.
# http://proguard.sourceforge.net/index.html#manual/usage.html
-ignorewarnings
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Optimization is turned off by default. <PERSON> does not like code run
# through the ProGuard optimize and preverify steps (and performs some
# of these optimizations on its own).
-dontoptimize
-dontpreverify
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.

-keepattributes InnerClasses

-optimizationpasses 5
-keepattributes *Annotation*
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}
#重要：web调用本地的相册，不要把系统的api混淆
-keepclassmembers class * extends android.webkit.WebChromeClient{
       public void openFileChooser(...);
}
-keep public class com.google.vending.licensing.ILicensingService
-keep public class com.android.vending.licensing.ILicensingService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep setters in Views so that animations can still work.
# see http://proguard.sourceforge.net/manual/examples.html#beans
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

-keepclassmembers class **.R$* {
    public static <fields>;
}

-keep public class com.ybmmarket20.R$*{
    public static final int *;
}

# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.
-dontwarn android.support.**

#泛型，解决出现类型转换错误的问题
-keepattributes Signature

#Serializable序列化
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
#Parcelable序列化
-keep class * implements android.os.Parcelable {
    public int describeContents();
    public static final android.os.Parcelable$Creator *;
    public void set*(***);
    public ** get*();
}
# Gson specific classes
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }
-keep class com.google.gson.** { *;}
#注解
-keepattributes *Annotation*
-keep class * extends java.lang.annotation.Annotation {*;}
# keep住源文件以及行号
-keepattributes SourceFile,LineNumberTable

#butterknife
-keep class butterknife.** { *; }
-dontwarn butterknife.internal.**
-keep class **$$ViewBinder { *; }

-keepclasseswithmembernames class * {
    @butterknife.* <fields>;
}

-keepclasseswithmembernames class * {
    @butterknife.* <methods>;
}
-dontwarn com.ybmmarket20.**
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn com.squareup.**
-dontwarn com.apkfuns.logutils.**
-keepclasseswithmembernames class * implements com.ybmmarket20.common.ViewOnClickListener { *; }
-keepclasseswithmembernames class * implements okhttp3.Callback { *; }
-keepclasseswithmembernames class * extends com.ybm.app.common.BaseCallback { *; }
-keepclasseswithmembernames class * extends android.os.Parcelable { *; }
-keep class com.ybmmarket20.bean.** { *; }
-keep class com.ybmmarket20.home.newpage.bean.** { *; }
-keep class com.ybm.app.bean.** { *; }
-keep class com.ybmmarketkotlin.bean.** { *; }
-keep class com.ybmmarket20.view.cascade.model.** { *; }
-keep class com.ybmmarket20.common.JGTrackTopLevelKt { *; }
-keep class com.ydmmarket.report.manager.TrackManager { *; }


#bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}
-keep class android.support.**{*;}
# Logger
-keep class com.tencent.mars.xlog.** { *; }
# Diagnose
-keep class com.tencent.tddiag.protocol.* { public *; }
-keep class com.tencent.tddiag.upload.UploadTask { public *; }

#bank
  -dontwarn com.unionpay.mobile.**
  -dontwarn org.apache.xerces.**
  -keep class com.unionpay.** {*;}
  -keep class org.simalliance.openmobileapi.** {*;}
  -keep class org.simalliance.openmobileapi.service.** {*;}

#activityrouter
-keep class com.github.mzule.activityrouter.router.** { *; }
-keep class com.bugtags.library.** {*;}
-dontwarn org.apache.http.**
-dontwarn android.net.http.AndroidHttpClient
-dontwarn com.bugtags.library.**

#Glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.integration.okhttp3.OkHttpGlideModule

#小米推送
-dontwarn com.xiaomi.push.**
-keep class com.ybm100.app.push.MiMessageReceiver {*;}

#base包
-keep class com.ybm.app.pluginCore.** { *; }

#讯飞
-keep class com.iflytek.**{*;}
-keepattributes Signature

#友盟
-dontwarn u.aly.**
-keep class u.aly.** { *; }
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}
-keep public class com.ybmmarket20.R$*{
public static final int *;
}
#极光
-dontwarn cn.jpush.**
-keep class cn.jpush.** { *; }
-keep class * extends cn.jpush.android.helpers.JPushMessageReceiver { *; }
-dontwarn cn.jiguang.**
-keep class cn.jiguang.** { *; }

#支付宝
#-dontwarn com.alipay.**
#-keep class com.alipay.android.app.IAlixPay{*;}
#-keep class com.alipay.android.app.IAlixPay$Stub{*;}
#-keep class com.alipay.android.app.IRemoteServiceCallback{*;}
#-keep class com.alipay.android.app.IRemoteServiceCallback$Stub{*;}
#-keep class com.alipay.sdk.app.PayTask{ public *;}
#-keep class com.alipay.sdk.app.AuthTask{ public *;}
#-keepresourcexmlelements manifest/application/meta-data@value=GlideModule
#-applymapping ../mapping/mapping.txt

#分享
-dontwarn com.umeng.**
-keep public class javax.**
-keep public interface com.tencent.**
-keep public interface com.umeng.socialize.**
-keep public interface com.umeng.socialize.sensor.**
-keep public interface com.umeng.scrshot.**

-keep public class com.umeng.socialize.* {*;}

-keep class com.umeng.scrshot.**
-keep public class com.tencent.** {*;}
-keep class com.umeng.socialize.sensor.**
-keep class com.umeng.socialize.handler.**
-keep class com.umeng.socialize.handler.*
-keep class com.umeng.weixin.handler.**
-keep class com.umeng.weixin.handler.*
-keep class com.umeng.qq.handler.**
-keep class com.umeng.qq.handler.*
-keep class UMMoreHandler{*;}
-keep class com.tencent.mm.sdk.modelmsg.WXMediaMessage {*;}
-keep class com.tencent.mm.sdk.modelmsg.** implements com.tencent.mm.sdk.modelmsg.WXMediaMessage$IMediaObject {*;}
-keep class com.tencent.mm.sdk.** {
   *;
}
-keep class com.tencent.mm.opensdk.** {
   *;
}
-keep class com.tencent.wxop.** {
   *;
}
-keep class com.tencent.mm.sdk.** {
   *;
}

-keep class com.tencent.** {*;}
-dontwarn com.tencent.**
-keep class com.kakao.** {*;}
-dontwarn com.kakao.**
-keep public class com.umeng.com.umeng.soexample.R$*{
    public static final int *;
}

-keep class com.tencent.open.TDialog$*
-keep class com.tencent.open.TDialog$* {*;}
-keep class com.tencent.open.PKDialog
-keep class com.tencent.open.PKDialog {*;}
-keep class com.tencent.open.PKDialog$*
-keep class com.tencent.open.PKDialog$* {*;}
-keep class com.umeng.socialize.impl.ImageImpl {*;}



-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

#retrofit
-dontwarn javax.annotation.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

#rxjava
-dontwarn sun.misc.**
-keepclassmembers class rx.internal.util.unsafe.*ArrayQueue*Field* {
    long producerIndex;
    long consumerIndex;
}

# ImmersionBar混淆规则
-keep class com.gyf.barlibrary.* {*;}
-dontwarn com.gyf.barlibrary.**

# 诸葛IO
-keep class com.zhuge.analysis.** { *; }

#XBanner
-keep class com.stx.xhb.xbanner.**{*;}
# IM
-keep class com.tencent.** { *; }
-keep class tv.danmaku.** { *; }


# httpdns 混淆
-keep class com.alibaba.sdk.android.**{*;}
-keep class com.ut.**{*;}
-keep class com.ta.**{*;}

# 百度地图避免二次混淆
-keep class com.baidu.location.** {*;}

#xyyPush推送混淆配置
-keep class com.xyy.push.**{*;}
#小米推送 （使用了混淆，你需要使用下面的代码keep自定义的BroadcastReceiver，app中定义的完整类名）
-keep class com.xyy.push.service.impl.XiaomiPushMsgReceiver {*;}
#可以防止一个误报的 warning 导致无法成功编译，如果编译使用的 Android 版本是 23。
-dontwarn com.xiaomi.push.**

#华为推送
-ignorewarnings
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keep class com.hianalytics.android.**{*;}
-keep class com.huawei.updatesdk.**{*;}
-keep class com.huawei.hms.**{*;}

# 支付宝支付分享问题
 -keep class com.alipay.share.sdk.** {*;}

# Flutter 混淆
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

-dontwarn dalvik.**
-dontwarn com.tencent.smtt.**
-keep class com.tencent.smtt.** {
    *;
}
-keep class com.tencent.tbs.** {
    *;
}

-keep class com.shockwave.**

-keepclassmembers class * {
    @com.luck.picture.lib.rxbus2.Subscribe <methods>;
}


#############################################
#
# KYB 混淆配置 Start
#
#############################################
# 不混淆log
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}
# Pax
-keep class com.pingan.bank.kyb_sdk_demo.** {
public <fields>;
public <methods>;
protected <methods>;
}

-keep class androidx.core.content.**{*;}
# 保持kybsdk加固壳不被混淆
-keep class com.secneo.**{*;}
-keep class com.bangcle.**{*;}

# KybSdk
#-dontwarn com.pingan.bank.kyb_sdk.**
-keep class com.pingan.bank.kyb_sdk.** { *; }
-keep class com.pingan.extend.** { *; }

# 微信分享
#-dontwarn com.tencent.smtt.**
-keep class com.tencent.mm.** {*;}
-keep class com.tencent.smtt.** {
	*;
}
-keep public interface com.tencent.**
-keep public class com.tencent.** {*;}
-keep class com.tencent.mm.sdk.modelmsg.WXMediaMessage {*;}
-keep class com.tencent.mm.sdk.modelmsg.** implements com.tencent.mm.sdk.modelmsg.WXMediaMessage$IMediaObject {*;}
-keep class com.tencent.mm.sdk.** {
   *;
}
-keep class com.tencent.mm.opensdk.** {
   *;
}
-keep class com.tencent.wxop.** {
   *;
}
-keep class com.tencent.mm.sdk.** {
   *;
}
-keep class com.tencent.** {*;}
-dontwarn com.tencent.**

# Glide
-dontwarn com.bumptech.glide.**
-keep class com.bumptech.glide.** {*;}
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.request.target.SimpleTarget

# 人脸识别
-keep class com.pingan.ai.** {*;}
-keep class pingan.ai.paverify.vertify.** {*;}

# PaxGo
-keep class com.pingan.bank.libs.paxgo.** { *; }
-keep class go.** { *; }
# Pax
-keep class com.pingan.bank.pax.** {
public <fields>;
public <methods>;
protected <methods>;
}

# Gson
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.** { *; }

#Okhttp
-keep class okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

#权限申请
-keep class com.kcode.**{*;}

#人脸识别
-keep class com.secneo.**{*;}
-keep class com.bangcle.**{*;}
-keep class com.rs.permission.**{*;}
-keep class com.pingan.pabrlib.**{*;}

#极光埋点start=====
-dontwarn com.analysys.**
-keep class com.analysys** {*;}

-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep public class com.ybmmarket20.R$*{
public static final int *;
}
#极光埋点start===== end

#############################################
#
# KYB 混淆配置 End
#
#############################################

#企业微信
-keep class com.tencent.wework.api.** {
   *;
}

-keep class com.umeng.** {*;}
-keep class org.repackage.** {*;}

-keep class com.quick.qt.** {*;}
-keep class rpk.quick.qt.** {*;}
-keep class com.ybmmarket20.xyyreport.spm.TrackData {*;}
-keep class com.ybmmarket20.xyyreport.** {*;}

-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
